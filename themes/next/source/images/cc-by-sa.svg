<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://web.resource.org/cc/"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="80"
   height="15"
   id="svg2279"
   sodipodi:version="0.32"
   inkscape:version="0.45+devel"
   version="1.0"
   sodipodi:docname="by-sa.svg"
   inkscape:output_extension="org.inkscape.output.svg.inkscape">
  <defs
     id="defs2281">
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath3442">
      <rect
         style="fill:#000000;fill-opacity:1;stroke:none;stroke-width:0.92243534;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect3444"
         width="20.614058"
         height="12.483703"
         x="171.99832"
         y="239.1203" />
    </clipPath>
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#999999"
     borderopacity="1"
     gridtolerance="10000"
     guidetolerance="10"
     objecttolerance="10"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="10.5125"
     inkscape:cx="40"
     inkscape:cy="7.5"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     width="80px"
     height="15px"
     showborder="true"
     inkscape:showpageshadow="false"
     inkscape:window-width="935"
     inkscape:window-height="624"
     inkscape:window-x="50"
     inkscape:window-y="160" />
  <metadata
     id="metadata2284">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1">
    <g
       id="BY-SA"
       transform="matrix(0.9875019,0,0,0.9333518,-323.90064,-356.81188)">
      <g
         id="g3747"
         transform="translate(158,145)">
        <rect
           style="fill:#ffffff;fill-opacity:1;stroke:#000000;stroke-width:1.04161763;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
           id="rect3749"
           width="80"
           height="15"
           x="170.5"
           y="237.86218" />
        <rect
           y="239.36218"
           x="172"
           height="12"
           width="77"
           id="rect3751"
           style="fill:#000000;fill-opacity:1;stroke:none;stroke-width:0.92243534;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
        <path
           style="fill:#abb1aa;fill-opacity:1;stroke:none;stroke-width:1;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:0.46913578"
           d="M 171.99996,239.37505 L 171.99996,251.37505 L 192.33474,251.37505 C 193.64339,249.62474 194.52652,247.59057 194.52652,245.37505 C 194.52652,243.17431 193.65859,241.1179 192.36599,239.37505 L 171.99996,239.37505 z"
           id="path3753"
           sodipodi:nodetypes="cccscc" />
        <g
           id="g3755"
           transform="matrix(0.9612533,0,0,0.9612533,6.8341566,9.5069994)"
           clip-path="url(#clipPath3442)">
          <path
             id="path3757"
             cx="296.35416"
             ry="22.939548"
             cy="264.3577"
             type="arc"
             rx="22.939548"
             d="M 190.06417,245.36206 C 190.06667,249.25405 186.91326,252.41072 183.02153,252.41323 C 179.12979,252.41572 175.97262,249.26256 175.97036,245.3706 C 175.97036,245.36783 175.97036,245.36507 175.97036,245.36206 C 175.9681,241.47007 179.12126,238.3134 183.013,238.31113 C 186.90524,238.30864 190.06191,241.46181 190.06417,245.3538 C 190.06417,245.35628 190.06417,245.35929 190.06417,245.36206 z"
             style="opacity:1;fill:#ffffff" />
          <path
             d="M 188.74576,239.62226 C 190.30843,241.18492 191.08988,243.09869 191.08988,245.36206 C 191.08988,247.62592 190.32197,249.51913 188.78615,251.04165 C 187.15627,252.64521 185.22995,253.44672 183.00722,253.44672 C 180.81132,253.44672 178.91837,252.65172 177.32887,251.06174 C 175.73912,249.47198 174.94436,247.57226 174.94436,245.36206 C 174.94436,243.15235 175.73912,241.23908 177.32887,239.62226 C 178.87799,238.0591 180.77094,237.27764 183.00722,237.27764 C 185.2706,237.27764 187.18312,238.05909 188.74576,239.62226 z M 178.38093,240.67355 C 177.05978,242.008 176.39945,243.57116 176.39945,245.36429 C 176.39945,247.15694 177.05326,248.70682 178.36062,250.01393 C 179.66822,251.32153 181.22487,251.97509 183.03105,251.97509 C 184.83724,251.97509 186.40716,251.31502 187.74161,249.99412 C 189.0086,248.76725 189.64234,247.22467 189.64234,245.36429 C 189.64234,243.51799 188.99831,241.95084 187.71101,240.66354 C 186.42396,239.37649 184.86406,238.7327 183.03105,238.7327 C 181.19804,238.73271 179.64767,239.37975 178.38093,240.67355 z M 181.85761,244.57559 C 181.65573,244.13545 181.35354,243.91525 180.95051,243.91525 C 180.23802,243.91525 179.8819,244.39501 179.8819,245.35404 C 179.8819,246.31328 180.23802,246.79255 180.95051,246.79255 C 181.421,246.79255 181.75705,246.55908 181.95869,246.09111 L 182.94629,246.61701 C 182.47555,247.45339 181.76934,247.87168 180.82763,247.87168 C 180.10136,247.87168 179.51953,247.64899 179.08265,247.20409 C 178.64502,246.7587 178.42684,246.14477 178.42684,245.36206 C 178.42684,244.59313 178.65204,243.98271 179.10271,243.53056 C 179.55338,243.07838 180.11463,242.8524 180.7875,242.8524 C 181.78288,242.8524 182.49561,243.24465 182.92647,244.02835 L 181.85761,244.57559 z M 186.50398,244.57559 C 186.30184,244.13545 186.00567,243.91525 185.61517,243.91525 C 184.88839,243.91525 184.52474,244.39501 184.52474,245.35404 C 184.52474,246.31328 184.88839,246.79255 185.61517,246.79255 C 186.08642,246.79255 186.41644,246.55908 186.6048,246.09111 L 187.61447,246.61701 C 187.14448,247.45339 186.43926,247.87168 185.49931,247.87168 C 184.77403,247.87168 184.19346,247.64899 183.75683,247.20409 C 183.32096,246.7587 183.10254,246.14477 183.10254,245.36206 C 183.10254,244.59313 183.32422,243.98271 183.76737,243.53056 C 184.21026,243.07838 184.77404,242.8524 185.4592,242.8524 C 186.45282,242.8524 187.16455,243.24465 187.5939,244.02835 L 186.50398,244.57559 z"
             id="path3759"
             style="opacity:1" />
        </g>
      </g>
      <path
         id="text3761"
         d="M 357.4197,389.68502 C 357.66518,389.68502 357.85131,389.63144 357.9781,389.52427 C 358.10488,389.4171 358.16827,389.25904 358.16828,389.05005 C 358.16827,388.84376 358.10488,388.68703 357.9781,388.57986 C 357.85131,388.47002 357.66518,388.4151 357.4197,388.41509 L 356.55784,388.41509 L 356.55784,389.68502 L 357.4197,389.68502 M 357.4723,392.30926 C 357.78522,392.30926 358.0199,392.24363 358.17637,392.11235 C 358.33552,391.98107 358.4151,391.78281 358.4151,391.51756 C 358.4151,391.25769 358.33686,391.06345 358.18041,390.93485 C 358.02396,390.80357 357.78792,390.73793 357.4723,390.73793 L 356.55784,390.73793 L 356.55784,392.30926 L 357.4723,392.30926 M 358.92089,390.15119 C 359.25538,390.24764 359.51434,390.42581 359.69779,390.68568 C 359.88121,390.94557 359.97293,391.26439 359.97294,391.64215 C 359.97293,392.22086 359.776,392.6522 359.38217,392.93619 C 358.98833,393.22018 358.38947,393.36218 357.5856,393.36218 L 355.00001,393.36218 L 355.00001,387.36218 L 357.33878,387.36218 C 358.17771,387.36218 358.78466,387.4881 359.15962,387.73994 C 359.53727,387.99178 359.7261,388.395 359.7261,388.94959 C 359.7261,389.24162 359.65732,389.49078 359.51975,389.69708 C 359.38217,389.90069 359.18255,390.05206 358.92089,390.15119 M 359.83746,387.36218 L 361.54096,387.36218 L 362.91671,389.50015 L 364.29245,387.36218 L 366,387.36218 L 363.69764,390.83438 L 363.69764,393.36218 L 362.13982,393.36218 L 362.13982,390.83438 L 359.83746,387.36218 M 365.15837,390.40839 L 367.69946,390.40839 L 367.69946,391.57785 L 365.15837,391.57785 L 365.15837,390.40839 M 373.12153,387.55105 L 373.12153,388.82099 C 372.78973,388.67363 372.46602,388.56245 372.15042,388.48743 C 371.8348,388.41241 371.53671,388.3749 371.25618,388.3749 C 370.88391,388.3749 370.60877,388.42581 370.43073,388.52761 C 370.25268,388.62943 370.16367,388.7875 370.16367,389.00183 C 370.16367,389.16259 370.22301,389.28851 370.3417,389.37959 C 370.4631,389.46801 370.68159,389.54436 370.99721,389.60866 L 371.66081,389.74127 C 372.33249,389.87524 372.80996,390.07886 373.0932,390.35213 C 373.37644,390.62541 373.51806,391.01389 373.51807,391.51756 C 373.51806,392.17933 373.3198,392.67229 372.92326,392.99647 C 372.52941,393.31797 371.92651,393.47872 371.11455,393.47872 C 370.7315,393.47872 370.3471,393.44255 369.96136,393.37021 C 369.5756,393.29788 369.18985,393.1907 368.80411,393.04871 L 368.80411,391.74262 C 369.18985,391.94624 369.56211,392.10029 369.92089,392.20477 C 370.28236,392.30658 370.63034,392.35749 370.96484,392.35749 C 371.30473,392.35749 371.56504,392.30123 371.74578,392.1887 C 371.92651,392.07618 372.01688,391.91542 372.01688,391.70645 C 372.01688,391.51891 371.95484,391.37423 371.83076,391.27242 C 371.70936,391.17062 371.46523,391.07952 371.09837,390.99915 L 370.49547,390.86653 C 369.89122,390.73793 369.44882,390.53297 369.16827,390.25166 C 368.89042,389.97035 368.7515,389.59125 368.7515,389.11435 C 368.7515,388.51691 368.94572,388.05743 369.33418,387.73592 C 369.72262,387.41442 370.28101,387.25367 371.00935,387.25367 C 371.34115,387.25367 371.68238,387.27912 372.03307,387.33003 C 372.38374,387.37826 372.74656,387.45193 373.12153,387.55105 M 378.55573,392.26907 L 376.11985,392.26907 L 375.73545,393.36218 L 374.16953,393.36218 L 376.40714,387.36218 L 378.2644,387.36218 L 380.50201,393.36218 L 378.93609,393.36218 L 378.55573,392.26907 M 376.5083,391.15588 L 378.16324,391.15588 L 377.3378,388.76874 L 376.5083,391.15588"
         style="font-size:8.25858784px;font-style:normal;font-weight:bold;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;font-family:'Bitstream Vera Sans'" />
    </g>
  </g>
</svg>
