.btn {
  display: inline-block;
  padding: 0 20px;
  font-size: 15px;
  color: $btn-default-color;
  background: $btn-default-bg;
  border: 2px solid $btn-default-border;
  text-decoration: none;
  transition-property: background-color;
  the-transition();

  &:hover {
    border-color: $btn-default-hover-border-color;
    color: $btn-default-hover-color;
    background: $btn-default-hover-bg;
  }
}

.btn-bar {
  display: block;
  width: 22px;
  height: 2px;
  background: $text-color;
  border-radius: 1px;

  &+.btn-bar { margin-top: 4px; }
}
