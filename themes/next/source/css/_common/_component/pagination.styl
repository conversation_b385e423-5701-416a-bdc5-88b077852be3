.pagination {
  margin: 120px 0 40px;
  text-align: center;
  border-top: 1px solid $pagination-border;
}

.page-number-basic {
  display: inline-block;
  position: relative;
  top: -1px;
  margin: 0 10px;
  padding: 0 10px;
  line-height: 30px;

  +mobile() { margin: 0 5px; }
}

.pagination {
  .prev, .next, .page-number {
    @extend .page-number-basic;
    border-bottom: 0;
    border-top: 1px solid $pagination-link-border;
    transition-property: border-color;
    the-transition();

    &:hover { border-top-color: $pagination-link-hover-border; }
  }

  .space {
    @extend .page-number-basic;
    padding: 0;
    margin: 0;
  }

  .prev { margin-left: 0; }
  .next { margin-right: 0; }

  .page-number.current {
    color: $pagination-active-color;
    background: $pagination-active-bg;
    border-top-color: $pagination-active-border;
  }
}

@media (max-width: 767px)
  .pagination { border-top: none; }
  
  .pagination {
    .prev, .next, .page-number {
      margin-bottom: 10px;
      border-top: 0;
      border-bottom: 1px solid $pagination-link-border;

      &:hover { border-bottom-color: $pagination-link-hover-border; }
    }
  }
