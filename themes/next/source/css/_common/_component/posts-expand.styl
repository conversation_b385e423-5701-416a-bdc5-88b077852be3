.posts-expand {
  padding-top: 40px;
}

@media (max-width: 767px) {
  .posts-expand {
    margin: 0 20px;
  }

  .post-body {
    pre, .highlight {
      margin 0 -20px
      padding 10px 20px
      .gutter pre {
        padding-right 10px
      }
    }
  }
}

.posts-expand .post-title {
  font-size: 26px;
  text-align: center;

  +mobile() {
    font-size: 22px;
  }
}
.posts-expand .post-title-link {
  display: inline-block;
  position: relative;
  color: $black-light;
  border-bottom: none;
  line-height: 1.2;
  vertical-align: top;

  &::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #000;
    visibility: hidden;
    transform: scaleX(0);
    the-transition();
  }

  &:hover::before {
    visibility: visible
    transform: scaleX(1);
  }
}

.posts-expand .post-meta {
  margin: 3px 0 60px 0;
  color: $grey-dark;
  font-size: 12px;
  text-align: center;

  .post-category-list {
    display inline-block
    margin 0
    padding 3px
  }
  .post-category-list-link {
    color: $grey-dark;
  }
}

.posts-expand .post-body {
  text-align: justify;

  h2, h3, h4, h5, h6 {
    padding-top: 10px;
  }

  img {
    box-sizing: border-box;
    margin: auto;
    padding: 3px;
    border: 1px solid $gray-lighter;
  }
}

.posts-expand .post-tags {
  margin-top: 40px;
  text-align: center;

  a {
    display inline-block
    margin-right 10px
    font-size: 13px
  }
}

.post-nav {
  overflow: hidden;
  margin-top: 60px;
  padding: 10px;
  white-space: nowrap;
  border-top: 1px solid $gainsboro;
}

.post-nav-item {
  display: inline-block;
  width: 50%;
  white-space: normal;

  a {
    position: relative;
    display: inline-block;
    line-height: 25px;
    font-size: 14px;
    color: $black-light;
    border-bottom: none;

    &:hover {
      color: $black-deep;
      font-weight: bold;
      border-bottom: none;
    }

    &:active {
      top: 2px;
    }

    &:before, &:after {
      display: inline-block;
      width: 16px;
      height: 25px;
      vertical-align: top;
      opacity: 0.4;
      background-size: 16px;
    }
  }

}

.post-nav-prev {
  a:before {
    content: ' ';
    background: url($chevron-left) no-repeat 0 50%;
    background-size: 8px;
  }
  a:hover:before {
    opacity: 1;
  }
}

.post-nav-next {
  text-align: right;

  a:after {
    content: ' ';
    background: url($chevron-right) no-repeat 100% 50%;
    background-size: 8px;
  }
  a:hover:after {
    opacity: 1;
  }
}

.posts-expand {
  .post-eof {
    display: block;
    margin 160px auto 120px
    width 8%
    height 1px
    background $grey-light
    text-align center
  }
}

.post-more-link {
  margin-top: 50px;
}
