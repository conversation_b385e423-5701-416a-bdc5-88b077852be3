@media (max-width: 767px) {
  .posts-collapse {
    margin: 0 20px;

    .post-title, .post-meta {
      display: block
      width: auto
      text-align left
    }
  }
}

.posts-collapse {

  margin-left: $posts-collpase-left;
  +mobile() { margin: 0 20px; }

  .collection-title {
    position: relative;
    margin: 60px 0;

    h2 { margin-left: 20px; }

    small { color: $grey; }

    &::before {
      content: " ";
      position: absolute;
      left: 0;
      top: 50%;
      margin-left: -4px;
      margin-top: -4px;
      width: 8px;
      height: 8px;
      background: $grey;
      circle();
    }
  }

  .post { margin: 30px 0; }

  .post-header {
    position: relative;
    the-transition();
    transition-property: border;
    border-bottom: 1px dashed $grey-light;

    &::before {
      content: " ";
      position: absolute;
      left: 0;
      top: 12px;
      width: 6px;
      height: 6px;
      margin-left: -4px;
      background: $grey;
      circle();
      border: 1px solid white;
      the-transition();
      transition-property: background;
    }
  }

  .post-header:hover {
    border-bottom-color: $grey-dim;

    &::before { background: $black-deep; }
  }

  .post-meta {
    position: absolute;
    font-size: 12px;
    left: 20px;
    top: 5px;
  }

  .post-comments-count { display: none; }

  .post-title {
    margin-left: 60px;
    font-size: 16px;
    font-weight: normal;
    font-family: $font-family-posts;
    line-height: inherit;

    &::after {
      margin-left: 3px;
      opacity: 0.6;
    }

    a {
      color: $grey-dim;
      border-bottom: none;
    }
  }
}
