
.theme-next {
  $duoshuoBaseBorderColor = #c7d4e1;
  $duoshuoBaseBgColor = #f6f8fa;

  #ds-thread #ds-reset {
    color: #555;
  }

  #ds-thread #ds-reset .ds-replybox {
    margin-bottom: 30px;
  }

  #ds-thread #ds-reset .ds-replybox .ds-avatar, #ds-reset .ds-avatar img {
    box-shadow: none;
  }

  #ds-thread #ds-reset .ds-textarea-wrapper {
    border-color: $duoshuoBaseBorderColor;
    background: none;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
  }


  #ds-thread #ds-reset .ds-textarea-wrapper textarea {
    height: 60px;
  }

  #ds-reset .ds-rounded-top {
    border-radius: 0;
  }

  #ds-thread #ds-reset .ds-post-toolbar {
    box-sizing: border-box;
    border: 1px solid $duoshuoBaseBorderColor;
    background: $duoshuoBaseBgColor;
  }

  #ds-thread #ds-reset .ds-post-options {
    height: 40px;
    border: none;
    background: none;
  }

  #ds-thread #ds-reset .ds-toolbar-buttons {
    top: 11px;
  }

  #ds-thread #ds-reset .ds-sync {
    top: 5px;
  }

  #ds-thread #ds-reset .ds-post-button {
    top: 4px;
    right: 5px;
    width: 90px;
    height: 30px;
    border: 1px solid #c5ced7;
    border-radius: 3px;
    background-image: linear-gradient(#fbfbfc, #f5f7f9);
    color: #60676d;
  }

  #ds-thread #ds-reset .ds-post-button:hover {
    background-position: 0 -30px;
    color: #60676d;
  }

  #ds-thread #ds-reset .ds-comments-info {
    padding: 10px 0;
  }

  #ds-thread #ds-reset .ds-sort {
    display: none;
  }

  #ds-thread #ds-reset li.ds-tab a.ds-current {
    border: none;
    background: $duoshuoBaseBgColor;
    color: #60676d;

    &:hover {
      background-color: #e9f0f7;
      color: #60676d;
    }
  }

  #ds-thread #ds-reset li.ds-tab a {
    border-radius: 2px;
    padding: 5px;
  }

  #ds-thread #ds-reset .ds-login-buttons p {
    color: #999;
    line-height: 36px;
  }

  #ds-thread #ds-reset .ds-login-buttons .ds-service-list li {
    height: 28px;
  }

  #ds-thread #ds-reset .ds-service-list a {
    background: none;
    padding: 5px;
    border: 1px solid;
    border-radius: 3px;
    text-align: center;

    &:hover {
      color: #fff;
      background: #666;
    }
  }

  #ds-thread #ds-reset .ds-service-list .ds-weibo {
    color: #fc9b00;
    border-color: #fc9b00;

    &:hover {
      background: #fc9b00;
    }
  }

  #ds-thread #ds-reset .ds-service-list .ds-qq {
    color: #60a3ec;
    border-color: #60a3ec;

    &:hover {
      background: #60a3ec;
    }
  }

  #ds-thread #ds-reset .ds-service-list .ds-renren {
    color: #2e7ac4;
    border-color: #2e7ac4;

    &:hover {
      background: #2e7ac4;
    }
  }

  #ds-thread #ds-reset .ds-service-list .ds-douban {
    color: #37994c;
    border-color: #37994c;

    &:hover {
      background: #37994c;
    }
  }
  #ds-thread #ds-reset .ds-service-list .ds-more-services {
    border: none;
    &:hover {
      background: none;
    }
  }
  
/*duoshuo UA style begin*/

.this_ua {
    background-color: #ccc !important;
    border-radius: 4px;
    padding: 0 5px !important;
    margin: 0 1px !important;
    border: 1px solid #BBB !important;
    color: #fff;
}

.this_ua.admin {
    background-color: #d9534f !important;
    border-color: #d9534f !important;
}

.this_ua.platform.iOS, .this_ua.platform.Mac, .this_ua.platform.Windows {
    background-color: #39b3d7 !important;
    border-color: #46b8da !important;
}

.this_ua.platform.Linux {
    background-color: #3A3A3A !important;
    border-color: #1F1F1F !important;
}

.this_ua.platform.Android {
    background-color: #00C47D !important;
    border-color: #01B171 !important;
}

.this_ua.browser.Mobile, .this_ua.browser.Chrome {
    background-color: #5cb85c !important;
    border-color: #4cae4c !important;
}

.this_ua.browser.Firefox {
    background-color: #f0ad4e !important;
    border-color: #eea236 !important;
}

.this_ua.browser.Maxthon, .this_ua.browser.IE {
    background-color: #428bca !important;
    border-color: #357ebd !important;
}

.this_ua.browser.baidu, .this_ua.browser.UCBrowser, .this_ua.browser.Opera {
    background-color: #d9534f !important;
    border-color: #d43f3a !important;
}

.this_ua.browser.Android, .this_ua.browser.QQBrowser {
    background-color: #78ACE9 !important;
    border-color: #4cae4c !important;
}

/*duoshuo UA style end*/
  
}
