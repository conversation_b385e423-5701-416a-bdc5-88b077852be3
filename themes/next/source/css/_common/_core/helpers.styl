//
// Helpers
// =================================================



// Alignment
.text-left    { text-align: left; }
.text-center  { text-align: center; }
.text-right   { text-align: right; }
.text-justify { text-align: justify; }
.text-nowrap  { white-space: nowrap; }


// Transformation
.text-lowercase  { text-transform: lowercase; }
.text-uppercase  { text-transform: uppercase; }
.text-capitalize { text-transform: capitalize; }


// Center-align a block level element.
.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}


// Clearfix. http://nicolasgallagher.com/micro-clearfix-hack/
.clearfix {
  clearfix();
}


// Expand image to 126% with nagative margin-left/right.
.full-image.full-image.full-image {
  border: none;
  +desktop() {
    max-width: none;
    width: 126%;
    margin: 0 -13%;
  }
}


// Blockquote with all children centered.
.blockquote-center {
  position: relative;
  margin: 40px 0;
  padding: 0;
  border-left: none;
  text-align: center;

  &::before, &::after {
    position: absolute;
    content: ' ';
    display: block;
    width: 100%;
    height: 24px;
    opacity: 0.2;
    background: url($blockquoteCenterIcon) no-repeat;
    background-position: 0 -7px;
    background-size: 24px 24px;
  }
  &::before {
    top: -20px;
    border-top: 1px solid $grey-light;
  }
  &::after {
    bottom: -20px;
    border-bottom: 1px solid $grey-light;
    background-position: 100% 7px;
  }

  p, div { text-align: center; }
}


.use-motion .motion-element { opacity: 0; }
