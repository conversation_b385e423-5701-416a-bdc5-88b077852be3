.use-motion {
  .post { opacity: 0; }
}

.page-archive {

  .archive-page-counter {
    position: relative;
    top: 3px;
    left: 20px;

    +mobile() {
      top: 5px;
    }
  }

  .posts-collapse {
    position: relative;

    &::after {
      top: 20px;
      left: 0;
      margin-left: -2px;
      width: 4px;
      height: 100%;
      background: $whitesmoke;
      z-index: $zindex-bottom;
    }

    .archive-move-on {
      position: absolute;
      top: 11px;
      left: 0;
      margin-left: -6px;
      width: 10px;
      height: 10px;
      opacity: 0.5;
      background: $black-light;
      border: 1px solid white;

      circle();
    }

    &::before, &::after {
      content: " ";
      position: absolute;
    }
  }
}
