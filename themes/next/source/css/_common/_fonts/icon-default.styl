// FONT-SIZE FOR default ICON SET.
// Duplicate class name to improve priority.

// Icons from IcoMoon

.icon-storage:before {
  content: "\e0bc";
}
.icon-insert_emoticon:before {
  content: "\e0e8";
}
.icon-brightness_4:before {
  content: "\e13e";
}
.icon-whatshot:before {
  content: "\e25b";
}
.icon-bookmark:before {
  content: "\e27b";
}
.icon-favorite_outline:before {
  content: "\e292";
}
.icon-home:before {
  content: "\e29e";
}
.icon-open_in_new:before {
  content: "\e2b2";
}
.icon-search:before {
  content: "\e2ca";
}
.icon-view_carousel:before {
  content: "\e2fd";
}



// Icons name used in NexT

.icon-next-logo:before {
  @extend .icon-brightness_4:before;
}

.icon-next-home:before {
  @extend .icon-home:before;
}

.icon-next-categories:before {
  @extend .icon-storage:before;
}

.icon-next-archives:before {
  @extend .icon-view_carousel:before;
}

.icon-next-about:before {
  @extend .icon-insert_emoticon:before;
}

.icon-next-heart:before {
  @extend .icon-favorite_outline:before;
}

.icon-next-feed:before {
  @extend .icon-whatshot:before;
}

.icon-next-tags:before {
  @extend .icon-bookmark:before;
}

.icon-next-external-link:before {
  @extend .icon-open_in_new:before;
}

.icon-next-search:before {
  @extend .icon-search:before;
}
