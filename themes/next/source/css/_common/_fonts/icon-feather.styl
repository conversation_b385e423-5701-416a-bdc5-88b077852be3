// FONT-SIZE FOR default ICON SET.
// Duplicate class name to improve priority.

.menu-item-icon.menu-item-icon.menu-item-icon { font-size: 18px; }

// Icons from IcoMoon

.icon-ribbon:before {
  content: "\e009";
}
.icon-signal:before {
  content: "\e011";
}
.icon-server:before {
  content: "\e022";
}
.icon-heart:before {
  content: "\e024";
}
.icon-layers:before {
  content: "\e031";
}
.icon-search:before {
  content: "\e036";
}
.icon-drop:before {
  content: "\e063";
}
.icon-head:before {
  content: "\e074";
}
.icon-flag:before {
  content: "\e108";
}
.icon-open:before {
  content: "\e128";
}


// Icons name used in NexT

.icon-next-logo:before {
  @extend .icon-drop:before;
}

.icon-next-home:before {
  @extend .icon-flag:before;
}

.icon-next-categories:before {
  @extend .icon-layers:before;
}

.icon-next-archives:before {
  @extend .icon-server:before;
}

.icon-next-about:before {
  @extend .icon-head:before;
}

.icon-next-heart:before {
  @extend .icon-heart:before;
}

.icon-next-feed:before {
  @extend .icon-signal:before;
}

.icon-next-tags:before {
  @extend .icon-ribbon:before;
}

.icon-next-external-link:before {
  @extend .icon-open:before;
}

.icon-next-search:before {
  @extend .icon-search:before;
}
