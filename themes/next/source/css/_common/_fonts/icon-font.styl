$iconFontPrefix = '../fonts/icon-' + hexo-config('icon_font') + '/icomoon';
$iconFontFamily = hexo-config('icon_font');
$icon-font-version = '9394c0';

@font-face {
  font-family: $iconFontFamily;
  src:url($iconFontPrefix + '.eot?-' + $icon-font-version);
  src:url($iconFontPrefix + '.eot?#iefix-' + $icon-font-version) format('embedded-opentype'),
  url($iconFontPrefix + '.woff?-' + $icon-font-version) format('woff'),
  url($iconFontPrefix + '.ttf?-' + $icon-font-version) format('truetype'),
  url($iconFontPrefix + '.svg?-' + $icon-font-version + '#' + $iconFontFamily) format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  font-family: $iconFontFamily;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ==== Default font-size/color for icons.

.icon-next-logo {
  font-size: 24px;
  color: white;
}

// Menu icons
.icon-next-home,
.icon-next-categories,
.icon-next-archives,
.icon-next-tags,
.icon-next-about,
.icon-next-feed,
.icon-next-search {
  font-size: 24px;
  color: black;
}

.icon-next-external-link { font-size: 14px; }

.icon-next-heart { font-size: 14px; }
