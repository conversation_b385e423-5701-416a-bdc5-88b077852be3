.headband {
  height: 3px;
  background: $headband-bg;
}

.header { background: $head-bg; }

.header-inner { position: relative; }

.site-meta {
  margin: 0;
  text-align: $site-meta-text-align;

  +mobile() { text-align: center; }
}

.brand {
  position: relative;
  display: inline-block;
  padding: 0 25px;
  color: $brand-color;
  background: $brand-bg;
  border-bottom: none;
  font-family: $brand-font-family;
}

.logo {
  display: inline-block;
  margin-right: 5px;
  line-height: 36px;
  vertical-align: top;
}

.site-title {
  display: inline-block;
  vertical-align: top;
  line-height: 36px;
  font-size: 20px;
  font-weight: normal;
}

.use-motion {
  .brand { opacity: 0; }

  .logo, .site-title {
    opacity: 0;
    position: relative;
    top: -10px;
  }
}


.site-nav-toggle {
  display: none;
  position: absolute;
  top: 10px;
  left: 10px;
  +mobile() {
    display: block;
  }

  button {
    margin-top: 2px;
    padding: 9px 10px;
    background: transparent;
    border: none;
  }
}

.site-nav {
  +mobile() {
    display: none;
    margin: 0 -10px;
    padding: 0 10px;
    clear: both;
    border-top: 1px solid $gray-lighter;
  }
  +tablet() { display: block !important; }
  +desktop() { display: block !important; }
}


// Menu
// --------------------------------------------------
.menu {
  margin-top: 20px;
  padding-left: 0;
  text-align: center;
}

.menu .menu-item {
  display: inline-block;
  margin-left: 20px;
  @media screen and (max-width: 767px) {
    margin-top: 10px;
  }

  &:first-child { margin-left: 0; }

  a {
    display: block;
    font-size: 13px;
    text-transform: capitalize;
    line-height: 1.5;
    border-bottom: 1px solid $menu-link-border;
    transition-property: border-color;
    the-transition();

    &:hover { border-bottom-color: $menu-link-hover-border; }
  }
}
