the-transition() {
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
  transition-delay: 0s;
}

mobile() {
  @media (max-width: 767px) {
    {block}
  }
}

tablet() {
  @media (min-width: 768px) and (max-width: 991px) {
    {block}
  }
}

desktop() {
  @media (min-width: 992px) {
    {block}
  }
}

circle() {
  border-radius: 50%;
}

random-color($min, $max) {
  return floor(math(0, 'random') * ($max - $min + 1) + $min);
}

// Clearfix. http://nicolasgallagher.com/micro-clearfix-hack/
clearfix() {
  &:before,
  &:after {
    content: " ";
    display: table;
  }
  &:after { clear: both; }
}
