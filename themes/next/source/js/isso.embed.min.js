!function(){var requirejs,require,define;!function(a){function m(a,b){return j.call(a,b)}function n(a,b){var c,d,e,f,g,i,j,k,m,n,o,p=b&&b.split("/"),q=h.map,r=q&&q["*"]||{};if(a&&"."===a.charAt(0))if(b){for(a=a.split("/"),g=a.length-1,h.nodeIdCompat&&l.test(a[g])&&(a[g]=a[g].replace(l,"")),a=p.slice(0,p.length-1).concat(a),m=0;m<a.length;m+=1)if(o=a[m],"."===o)a.splice(m,1),m-=1;else if(".."===o){if(1===m&&(".."===a[2]||".."===a[0]))break;m>0&&(a.splice(m-1,2),m-=2)}a=a.join("/")}else 0===a.indexOf("./")&&(a=a.substring(2));if((p||r)&&q){for(c=a.split("/"),m=c.length;m>0;m-=1){if(d=c.slice(0,m).join("/"),p)for(n=p.length;n>0;n-=1)if(e=q[p.slice(0,n).join("/")],e&&(e=e[d])){f=e,i=m;break}if(f)break;!j&&r&&r[d]&&(j=r[d],k=m)}!f&&j&&(f=j,i=k),f&&(c.splice(0,i,f),a=c.join("/"))}return a}function o(b,d){return function(){var e=k.call(arguments,0);return"string"!=typeof e[0]&&1===e.length&&e.push(null),c.apply(a,e.concat([b,d]))}}function p(a){return function(b){return n(b,a)}}function q(a){return function(b){f[a]=b}}function r(c){if(m(g,c)){var d=g[c];delete g[c],i[c]=!0,b.apply(a,d)}if(!m(f,c)&&!m(i,c))throw new Error("No "+c);return f[c]}function s(a){var b,c=a?a.indexOf("!"):-1;return c>-1&&(b=a.substring(0,c),a=a.substring(c+1,a.length)),[b,a]}function t(a){return function(){return h&&h.config&&h.config[a]||{}}}var b,c,d,e,f={},g={},h={},i={},j=Object.prototype.hasOwnProperty,k=[].slice,l=/\.js$/;d=function(a,b){var c,d=s(a),e=d[0];return a=d[1],e&&(e=n(e,b),c=r(e)),e?a=c&&c.normalize?c.normalize(a,p(b)):n(a,b):(a=n(a,b),d=s(a),e=d[0],a=d[1],e&&(c=r(e))),{f:e?e+"!"+a:a,n:a,pr:e,p:c}},e={require:function(a){return o(a)},exports:function(a){var b=f[a];return"undefined"!=typeof b?b:f[a]={}},module:function(a){return{id:a,uri:"",exports:f[a],config:t(a)}}},b=function(b,c,h,j){var k,l,n,p,s,v,t=[],u=typeof h;if(j=j||b,"undefined"===u||"function"===u){for(c=!c.length&&h.length?["require","exports","module"]:c,s=0;s<c.length;s+=1)if(p=d(c[s],j),l=p.f,"require"===l)t[s]=e.require(b);else if("exports"===l)t[s]=e.exports(b),v=!0;else if("module"===l)k=t[s]=e.module(b);else if(m(f,l)||m(g,l)||m(i,l))t[s]=r(l);else{if(!p.p)throw new Error(b+" missing "+l);p.p.load(p.n,o(j,!0),q(l),{}),t[s]=f[l]}n=h?h.apply(f[b],t):void 0,b&&(k&&k.exports!==a&&k.exports!==f[b]?f[b]=k.exports:n===a&&v||(f[b]=n))}else b&&(f[b]=h)},requirejs=require=c=function(f,g,i,j,k){if("string"==typeof f)return e[f]?e[f](g):r(d(f,g).f);if(!f.splice){if(h=f,h.deps&&c(h.deps,h.callback),!g)return;g.splice?(f=g,g=i,i=null):f=a}return g=g||function(){},"function"==typeof i&&(i=j,j=k),j?b(a,f,g,i):setTimeout(function(){b(a,f,g,i)},4),c},c.config=function(a){return c(a)},requirejs._defined=f,define=function(a,b,c){if("string"!=typeof a)throw new Error("See almond README: incorrect module build, no module name");b.splice||(c=b,b=[]),m(f,a)||m(g,a)||(g[a]=[a,b,c])},define.amd={jQuery:!0}}(),define("components/almond/almond",function(){}),define("app/lib/ready",[],function(){"use strict";var a=!1,b=function(b){a||(a=!0,b())},c=function(a){document.addEventListener("DOMContentLoaded",function(){b(a)}),("interactive"===document.readyState||"complete"===document.readyState)&&b(a)};return c}),define("app/config",[],function(){"use strict";var c,d,e,a={css:!0,lang:(navigator.language||navigator.userLanguage).split("-")[0],"reply-to-self":!1,"require-email":!1,"require-author":!1,"max-comments-top":"inf","max-comments-nested":5,"reveal-on-click":5,avatar:!0,"avatar-bg":"#f0f0f0","avatar-fg":["#9abf88","#5698c4","#e279a3","#9163b6","#be5168","#f19670","#e4bf80","#447c69"].join(" "),vote:!0,"vote-levels":null},b=document.getElementsByTagName("script");for(c=0;c<b.length;c++)for(d=0;d<b[c].attributes.length;d++)if(e=b[c].attributes[d],/^data-isso-/.test(e.name))try{a[e.name.substring(10)]=JSON.parse(e.value)}catch(f){a[e.name.substring(10)]=e.value}return a["avatar-fg"]=a["avatar-fg"].split(" "),a}),define("app/i18n/bg",{"postbox-text":"Въведете коментара си тук (поне 3 знака)","postbox-author":"Име/псевдоним (незадължително)","postbox-email":"Ел. поща (незадължително)","postbox-website":"Уебсайт (незадължително)","postbox-submit":"Публикуване","num-comments":"1 коментар\n{{ n }} коментара","no-comments":"Все още няма коментари","comment-reply":"Отговор","comment-edit":"Редактиране","comment-save":"Запис","comment-delete":"Изтриване","comment-confirm":"Потвърждение","comment-close":"Затваряне","comment-cancel":"Отказ","comment-deleted":"Коментарът е изтрит.","comment-queued":"Коментарът чака на опашката за модериране.","comment-anonymous":"анонимен","comment-hidden":"{{ n }} скрити","date-now":"сега","date-minute":"преди 1 минута\nпреди {{ n }} минути","date-hour":"преди 1 час\nпреди {{ n }} часа","date-day":"вчера\nпреди {{ n }} дни","date-week":"миналата седмица\nпреди {{ n }} седмици","date-month":"миналия месец\nпреди {{ n }} месеца","date-year":"миналата година\nпреди {{ n }} години"}),define("app/i18n/cs",{"postbox-text":"Sem napiště svůj komentář (nejméně 3 znaky)","postbox-author":"Jméno (nepovinné)","postbox-email":"E-mail (nepovinný)","postbox-website":"Web (nepovinný)","postbox-submit":"Publikovat","num-comments":"Jeden komentář\n{{ n }} Komentářů","no-comments":"Zatím bez komentářů","comment-reply":"Odpovědět","comment-edit":"Upravit","comment-save":"Uložit","comment-delete":"Smazat","comment-confirm":"Potvrdit","comment-close":"Zavřít","comment-cancel":"Zrušit","comment-deleted":"Komentář smazán","comment-queued":"Komentář ve frontě na schválení","comment-anonymous":"Anonym","comment-hidden":"{{ n }} skryto","date-now":"právě teď","date-minute":"před minutou\npřed {{ n }} minutami","date-hour":"před hodinou\npřed {{ n }} hodinami","date-day":"včera\npřed {{ n }} dny","date-week":"minulý týden\npřed {{ n }} týdny","date-month":"minulý měsíc\npřed {{ n }} měsíci","date-year":"minulý rok\npřed {{ n }} lety"}),define("app/i18n/de",{"postbox-text":"Kommentar hier eintippen (mindestens 3 Zeichen)","postbox-author":"Name (optional)","postbox-email":"Email (optional)","postbox-website":"Website (optional)","postbox-submit":"Abschicken","num-comments":"1 Kommentar\n{{ n }} Kommentare","no-comments":"Keine Kommentare bis jetzt","comment-reply":"Antworten","comment-edit":"Bearbeiten","comment-save":"Speichern","comment-delete":"Löschen","comment-confirm":"Bestätigen","comment-close":"Schließen","comment-cancel":"Abbrechen","comment-deleted":"Kommentar gelöscht.","comment-queued":"Kommentar muss noch freigeschaltet werden.","comment-anonymous":"Anonym","comment-hidden":"{{ n }} versteckt","date-now":"eben jetzt","date-minute":"vor einer Minute\nvor {{ n }} Minuten","date-hour":"vor einer Stunde\nvor {{ n }} Stunden","date-day":"Gestern\nvor {{ n }} Tagen","date-week":"letzte Woche\nvor {{ n }} Wochen","date-month":"letzten Monat\nvor {{ n }} Monaten","date-year":"letztes Jahr\nvor {{ n }} Jahren"}),define("app/i18n/en",{"postbox-text":"Type Comment Here (at least 3 chars)","postbox-author":"Name (optional)","postbox-email":"E-mail (optional)","postbox-website":"Website (optional)","postbox-submit":"Submit","num-comments":"One Comment\n{{ n }} Comments","no-comments":"No Comments Yet","comment-reply":"Reply","comment-edit":"Edit","comment-save":"Save","comment-delete":"Delete","comment-confirm":"Confirm","comment-close":"Close","comment-cancel":"Cancel","comment-deleted":"Comment deleted.","comment-queued":"Comment in queue for moderation.","comment-anonymous":"Anonymous","comment-hidden":"{{ n }} Hidden","date-now":"right now","date-minute":"a minute ago\n{{ n }} minutes ago","date-hour":"an hour ago\n{{ n }} hours ago","date-day":"Yesterday\n{{ n }} days ago","date-week":"last week\n{{ n }} weeks ago","date-month":"last month\n{{ n }} months ago","date-year":"last year\n{{ n }} years ago"}),define("app/i18n/fi",{"postbox-text":"Kirjoita kommentti tähän (vähintään 3 merkkiä)","postbox-author":"Nimi (valinnainen)","postbox-email":"Sähköposti (valinnainen)","postbox-website":"Web-sivu (valinnainen)","postbox-submit":"Lähetä","num-comments":"Yksi kommentti\n{{ n }} kommenttia","no-comments":"Ei vielä kommentteja","comment-reply":"Vastaa","comment-edit":"Muokkaa","comment-save":"Tallenna","comment-delete":"Poista","comment-confirm":"Vahvista","comment-close":"Sulje","comment-cancel":"Peru","comment-deleted":"Kommentti on poistettu.","comment-queued":"Kommentti on laitettu jonoon odottamaan moderointia.","comment-anonymous":"Nimetön","comment-hidden":"{{ n }} piilotettua","date-now":"hetki sitten","date-minute":"minuutti sitten\n{{ n }} minuuttia sitten","date-hour":"tunti sitten\n{{ n }} tuntia sitten","date-day":"eilen\n{{ n }} päivää sitten","date-week":"viime viikolla\n{{ n }} viikkoa sitten","date-month":"viime kuussa\n{{ n }} kuukautta sitten","date-year":"viime vuonna\n{{ n }} vuotta sitten"}),define("app/i18n/fr",{"postbox-text":"Insérez votre commentaire ici (au moins 3 lettres)","postbox-author":"Nom (optionnel)","postbox-email":"Courriel (optionnel)","postbox-website":"Site web (optionnel)","postbox-submit":"Soumettre","num-comments":"{{ n }} commentaire\n{{ n }} commentaires","no-comments":"Aucun commentaire pour l'instant","comment-reply":"Répondre","comment-edit":"Éditer","comment-save":"Enregistrer","comment-delete":"Supprimer","comment-confirm":"Confirmer","comment-close":"Fermer","comment-cancel":"Annuler","comment-deleted":"Commentaire supprimé.","comment-queued":"Commentaire en attente de modération.","comment-anonymous":"Anonyme","comment-hidden":"1 caché\n{{ n }} cachés","date-now":"À l'instant","date-minute":"Il y a une minute\nIl y a {{ n }} minutes","date-hour":"Il y a une heure\nIl y a {{ n }} heures ","date-day":"Hier\nIl y a {{ n }} jours","date-week":"Il y a une semaine\nIl y a {{ n }} semaines","date-month":"Il y a un mois\nIl y a {{ n }} mois","date-year":"Il y a un an\nIl y a {{ n }} ans"}),define("app/i18n/hr",{"postbox-text":"Napiši komentar ovdje (najmanje 3 znaka)","postbox-author":"Ime (neobavezno)","postbox-email":"E-mail (neobavezno)","postbox-website":"Web stranica (neobavezno)","postbox-submit":"Pošalji","num-comments":"Jedan komentar\n{{ n }} komentara","no-comments":"Još nema komentara","comment-reply":"Odgovori","comment-edit":"Uredi","comment-save":"Spremi","comment-delete":"Obriši","comment-confirm":"Potvrdi","comment-close":"Zatvori","comment-cancel":"Odustani","comment-deleted":"Komentar obrisan","comment-queued":"Komentar u redu za provjeru.","comment-anonymous":"Anonimno","comment-hidden":"{{ n }} Skrivenih","date-now":"upravo","date-minute":"prije minutu\nprije {{ n }} minuta","date-hour":"prije sat vremena\nprije {{ n }} sati","date-day":"jučer\nprije {{ n }} dana","date-week":"prošli tjedan\nprije {{ n }} tjedana","date-month":"prošli mjesec\nprije {{ n }} mjeseci","date-year":"prošle godine\nprije {{ n }} godina"}),define("app/i18n/ru",{"postbox-text":"Оставить комментарий (минимум 3 символа)","postbox-author":"Имя (необязательно)","postbox-email":"Email (необязательно)","postbox-website":"Сайт (необязательно)","postbox-submit":"Отправить","num-comments":"{{ n }} комментарий\n{{ n }} комментария\n{{ n }} комментариев","no-comments":"Пока нет комментариев","comment-reply":"Ответить","comment-edit":"Правка","comment-save":"Сохранить","comment-delete":"Удалить","comment-confirm":"Подтвердить удаление","comment-close":"Закрыть","comment-cancel":"Отменить","comment-deleted":"Комментарий удалён","comment-queued":"Комментарий будет проверен модератором","comment-anonymous":"Аноним","comment-hidden":"Скрыт {{ n }} комментарий\nСкрыто {{ n }} комментария\nСкрыто {{ n }} комментариев","date-now":"Только что","date-minute":"{{ n }} минуту назад\n{{ n }} минуты назад\n{{ n }} минут назад","date-hour":"{{ n }} час назад\n{{ n }} часа назад\n{{ n }} часов назад","date-day":"{{ n }} день назад\n{{ n }} дня назад\n{{ n }} дней назад","date-week":"{{ n }} неделю назад\n{{ n }} недели назад\n{{ n }} недель назад","date-month":"{{ n }} месяц назад\n{{ n }} месяца назад\n{{ n }} месяцев назад","date-year":"{{ n }} год назад\n{{ n }} года назад\n{{ n }} лет назад"}),define("app/i18n/it",{"postbox-text":"Scrivi un commento qui (minimo 3 caratteri)","postbox-author":"Nome (opzionale)","postbox-email":"E-mail (opzionale)","postbox-website":"Sito web (opzionale)","postbox-submit":"Invia","num-comments":"Un Commento\n{{ n }} Commenti","no-comments":"Ancora Nessun Commento","comment-reply":"Rispondi","comment-edit":"Modifica","comment-save":"Salva","comment-delete":"Elimina","comment-confirm":"Conferma","comment-close":"Chiudi","comment-cancel":"Cancella","comment-deleted":"Commento eliminato.","comment-queued":"Commento in coda per moderazione.","comment-anonymous":"Anonimo","comment-hidden":"{{ n }} Nascosto","date-now":"poco fa","date-minute":"un minuto fa\n{{ n }} minuti fa","date-hour":"un ora fa\n{{ n }} ore fa","date-day":"Ieri\n{{ n }} giorni fa","date-week":"questa settimana\n{{ n }} settimane fa","date-month":"questo mese\n{{ n }} mesi fa","date-year":"quest'anno\n{{ n }} anni fa"}),define("app/i18n/eo",{"postbox-text":"Tajpu komenton ĉi-tie (almenaŭ 3 signoj)","postbox-author":"Nomo (malnepra)","postbox-email":"Retadreso (malnepra)","postbox-website":"Retejo (malnepra)","postbox-submit":"Sendu","num-comments":"{{ n }} komento\n{{ n }} komentoj","no-comments":"Neniu komento ankoraŭ","comment-reply":"Respondu","comment-edit":"Redaktu","comment-save":"Savu","comment-delete":"Forviŝu","comment-confirm":"Konfirmu","comment-close":"Fermu","comment-cancel":"Malfaru","comment-deleted":"Komento forviŝita","comment-queued":"Komento en atendovico por kontrolo.","comment-anonymous":"Sennoma","comment-hidden":"{{ n }} kaŝitaj","date-now":"ĵus nun","date-minute":"antaŭ unu minuto\nantaŭ {{ n }} minutoj","date-hour":"antaŭ unu horo\nantaŭ {{ n }} horoj","date-day":"hieraŭ\nantaŭ {{ n }} tagoj","date-week":"lasta semajno\nantaŭ {{ n }} semajnoj","date-month":"lasta monato\nantaŭ {{ n }} monatoj","date-year":"lasta jaro\nantaŭ {{ n }} jaroj"}),define("app/i18n/sv",{"postbox-text":"Skriv din kommentar här (minst 3 tecken)","postbox-author":"Namn (frivilligt)","postbox-email":"E-mail (frivilligt)","postbox-website":"Hemsida (frivilligt)","postbox-submit":"Skicka","num-comments":"En kommentar\n{{ n }} kommentarer","no-comments":"Inga kommentarer än","comment-reply":"Svara","comment-edit":"Redigera","comment-save":"Spara","comment-delete":"Radera","comment-confirm":"Bekräfta","comment-close":"Stäng","comment-cancel":"Avbryt","comment-deleted":"Kommentar raderad.","comment-queued":"Kommentaren inväntar granskning.","comment-anonymous":"Anonym","comment-hidden":"{{ n }} Gömd","date-now":"just nu","date-minute":"en minut sedan\n{{ n }} minuter sedan","date-hour":"en timme sedan\n{{ n }} timmar sedan","date-day":"igår\n{{ n }} dagar sedan","date-week":"förra veckan\n{{ n }} veckor sedan","date-month":"förra månaden\n{{ n }} månader sedan","date-year":"förra året\n{{ n }} år sedan"}),define("app/i18n/nl",{"postbox-text":"Typ reactie hier (minstens 3 karakters)","postbox-author":"Naam (optioneel)","postbox-email":"E-mail (optioneel)","postbox-website":"Website (optioneel)","postbox-submit":"Versturen","num-comments":"Één reactie\n{{ n }} reacties","no-comments":"Nog geen reacties","comment-reply":"Beantwoorden","comment-edit":"Bewerken","comment-save":"Opslaan","comment-delete":"Verwijderen","comment-confirm":"Bevestigen","comment-close":"Sluiten","comment-cancel":"Annuleren","comment-deleted":"Reactie verwijderd.","comment-queued":"Reactie staat in de wachtrij voor goedkeuring.","comment-anonymous":"Anoniem","comment-hidden":"{{ n }} verborgen","date-now":"zojuist","date-minute":"een minuut geleden\n{{ n }} minuten geleden","date-hour":"een uur geleden\n{{ n }} uur geleden","date-day":"gisteren\n{{ n }} dagen geleden","date-week":"vorige week\n{{ n }} weken geleden","date-month":"vorige maand\n{{ n }} maanden geleden","date-year":"vorig jaar\n{{ n }} jaar geleden"}),define("app/i18n/el_GR",{"postbox-text":"Γράψτε το σχόλιο εδώ (τουλάχιστον 3 χαρακτήρες)","postbox-author":"Όνομα (προαιρετικό)","postbox-email":"E-mail (προαιρετικό)","postbox-website":"Ιστοσελίδα (προαιρετικό)","postbox-submit":"Υποβολή","num-comments":"Ένα σχόλιο\n{{ n }} σχόλια","no-comments":"Δεν υπάρχουν σχόλια","comment-reply":"Απάντηση","comment-edit":"Επεξεργασία","comment-save":"Αποθήκευση","comment-delete":"Διαγραφή","comment-confirm":"Επιβεβαίωση","comment-close":"Κλείσιμο","comment-cancel":"Ακύρωση","comment-deleted":"Διαγραμμένο σχόλιο ","comment-queued":"Το σχόλιο αναμένει έγκριση","comment-anonymous":"Ανώνυμος","comment-hidden":"{{ n }} Κρυμμένα","date-now":"τώρα","date-minute":"πριν ένα λεπτό\nπριν {{ n }} λεπτά","date-hour":"πριν μία ώρα\nπριν {{ n }} ώρες","date-day":"Χτες\nπριν {{ n }} μέρες","date-week":"την προηγούμενη εβδομάδα\nπριν {{ n }} εβδομάδες","date-month":"τον προηγούμενο μήνα\nπριν {{ n }} μήνες","date-year":"πέρυσι\nπριν {{ n }} χρόνια"}),define("app/i18n/es",{"postbox-text":"Escriba su comentario aquí (al menos 3 caracteres)","postbox-author":"Nombre (opcional)","postbox-email":"E-mail (opcional)","postbox-website":"Sitio web (opcional)","postbox-submit":"Enviar","num-comments":"Un Comentario\n{{ n }} Comentarios","no-comments":"Sin Comentarios Todavía","comment-reply":"Responder","comment-edit":"Editar","comment-save":"Guardar","comment-delete":"Eliminar","comment-confirm":"Confirmar","comment-close":"Cerrar","comment-cancel":"Cancelar","comment-deleted":"Comentario eliminado.","comment-queued":"Comentario en espera para moderación.","comment-anonymous":"Anónimo","comment-hidden":"{{ n }} Oculto(s)","date-now":"ahora","date-minute":"hace un minuto\nhace {{ n }} minutos","date-hour":"hace una hora\nhace {{ n }} horas","date-day":"ayer\nHace {{ n }} días","date-week":"la semana pasada\nhace {{ n }} semanas","date-month":"el mes pasado\nhace {{ n }} meses","date-year":"el año pasado\nhace {{ n }} años"}),define("app/i18n/vi",{"postbox-text":"Nhập bình luận tại đây (tối thiểu 3 ký tự)","postbox-author":"Tên (tùy chọn)","postbox-email":"E-mail (tùy chọn)","postbox-website":"Website (tùy chọn)","postbox-submit":"Gửi","num-comments":"Một bình luận\n{{ n }} bình luận","no-comments":"Chưa có bình luận nào","comment-reply":"Trả lời","comment-edit":"Sửa","comment-save":"Lưu","comment-delete":"Xóa","comment-confirm":"Xác nhận","comment-close":"Đóng","comment-cancel":"Hủy","comment-deleted":"Đã xóa bình luận.","comment-queued":"Bình luận đang chờ duyệt","comment-anonymous":"Nặc danh","comment-hidden":"{{ n }} đã ẩn","date-now":"vừa mới","date-minute":"một phút trước\n{{ n }} phút trước","date-hour":"một giờ trước\n{{ n }} giờ trước","date-day":"Hôm qua\n{{ n }} ngày trước","date-week":"Tuần qua\n{{ n }} tuần trước","date-month":"Tháng trước\n{{ n }} tháng trước","date-year":"Năm trước\n{{ n }} năm trước"}),define("app/i18n/zh_CN",{"postbox-text":"在此输入评论 (最少3个字符，支持MarkDown)","postbox-author":"名字 (可选)","postbox-email":"E-mail (可选)","postbox-website":"网站 (可选)","postbox-submit":"提交","num-comments":"1条评论\n{{ n }}条评论","no-comments":"还没有评论","comment-reply":"回复","comment-edit":"编辑","comment-save":"保存","comment-delete":"删除","comment-confirm":"确认","comment-close":"关闭","comment-cancel":"取消","comment-deleted":"评论已删除.","comment-queued":"评论待审核.","comment-anonymous":"匿名","comment-hidden":"{{ n }} 条评论已隐藏","date-now":"刚刚","date-minute":"1分钟前\n{{ n }}分钟前","date-hour":"1小时前\n{{ n }}小时前","date-day":"昨天\n{{ n }}天前","date-week":"上周\n{{ n }}周前","date-month":"上个月\n{{ n }}个月前","date-year":"去年\n{{ n }}年前"}),define("app/i18n",["app/config","app/i18n/bg","app/i18n/cs","app/i18n/de","app/i18n/en","app/i18n/fi","app/i18n/fr","app/i18n/hr","app/i18n/ru","app/i18n/it","app/i18n/eo","app/i18n/sv","app/i18n/nl","app/i18n/el_GR","app/i18n/es","app/i18n/vi","app/i18n/zh_CN"],function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q){"use strict";var t,u,v,w,r=function(a){switch(a){case"bg":case"cs":case"de":case"el":case"en":case"es":case"eo":case"fi":case"hr":case"it":case"sv":case"nl":case"vi":case"zh":return function(a,b){return a[1===b?0:1]};case"fr":return function(a,b){return a[b>1?1:0]};case"ru":return function(a,b){return 1===b%10&&11!==b%100?a[0]:b%10>=2&&4>=b%10&&(10>b%100||b%100>=20)?a[1]:"undefined"!=typeof a[2]?a[2]:a[1]};default:return null}},s=a.lang;return r(s)||(s="en"),t={cs:c,de:d,el:n,en:e,eo:k,es:o,fi:f,fr:g,it:j,hr:h,ru:i,sv:l,nl:m,vi:p,zh:q},u=r(s),v=function(a){return t[s][a]||e[a]||"???"},w=function(a,b){var c;return c=v(a),c.indexOf("\n")>-1&&(c=u(c.split("\n"),+b)),c?c.replace("{{ n }}",+b):c},{lang:s,translate:v,pluralize:w}}),define("app/lib/promise",[],function(){"use strict";var c,d,a=function(a){console.log(a)},b=function(){this.success=[],this.errors=[]};return b.prototype.then=function(b,c){this.success.push(b),c?this.errors.push(c):this.errors.push(a)},c=function(){this.promise=new b},c.prototype={promise:b,resolve:function(a){this.promise.success.forEach(function(b){window.setTimeout(function(){b(a)},0)})},reject:function(a){this.promise.errors.forEach(function(b){window.setTimeout(function(){b(a)},0)})}},d=function(a,c){return a instanceof b?a.then(c):c(a)},{defer:function(){return new c},when:d}}),define("app/globals",[],function(){"use strict";var a=function(){this.values=[]};return a.prototype.update=function(a){this.values.push((new Date).getTime()-a.getTime())},a.prototype.localTime=function(){return new Date((new Date).getTime()-this.values.reduce(function(a,b){return a+b})/this.values.length)},{offset:new a}}),define("app/api",["app/lib/promise","app/globals"],function(a,b){"use strict";var e,f,h,i,j,k,l,m,n,o,p,q,r,c="Eech7co8Ohloopo9Ol6baimi",d=window.location.pathname,g=document.getElementsByTagName("script");for(h=0;h<g.length;h++)if(g[h].hasAttribute("data-isso")){f=g[h].getAttribute("data-isso");break}if(!f){for(h=0;h<g.length;h++)if(g[h].getAttribute("async")||g[h].getAttribute("defer"))throw"Isso's automatic configuration detection failed, please refer to https://github.com/posativ/isso#client-configuration and add a custom `data-isso` attribute.";e=g[g.length-1],f=e.src.substring(0,e.src.length-"/js/embed.min.js".length)}return"/"===f[f.length-1]&&(f=f.substring(0,f.length-1)),i=function(a,c,d,e,f){function h(){var c,a=g.getResponseHeader("Date");null!==a&&b.offset.update(new Date(a)),c=g.getResponseHeader("X-Set-Cookie"),c&&c.match(/^isso-/)&&(document.cookie=c),g.status>=500?f&&f(g.body):e({status:g.status,body:g.responseText})}var g=new XMLHttpRequest;try{g.open(a,c,!0),g.withCredentials=!0,g.setRequestHeader("Content-Type","application/json"),g.onreadystatechange=function(){4===g.readyState&&h()}}catch(i){(f||console.log)(i.message)}g.send(d)},j=function(a){var c,b="";for(c in a)a.hasOwnProperty(c)&&null!==a[c]&&"undefined"!=typeof a[c]&&(b+=c+"="+encodeURIComponent(a[c])+"&");return b.substring(0,b.length-1)},k=function(b,c){var e=a.defer();return i("POST",f+"/new?"+j({uri:b||d}),JSON.stringify(c),function(a){201===a.status||202===a.status?e.resolve(JSON.parse(a.body)):e.reject(a.body)}),e.promise},l=function(b,c){var d=a.defer();return i("PUT",f+"/id/"+b,JSON.stringify(c),function(a){403===a.status?d.reject("Not authorized to modify this comment!"):200===a.status?d.resolve(JSON.parse(a.body)):d.reject(a.body)}),d.promise},m=function(b){var c=a.defer();return i("DELETE",f+"/id/"+b,null,function(a){403===a.status?c.reject("Not authorized to remove this comment!"):200===a.status?c.resolve(null===JSON.parse(a.body)):c.reject(a.body)}),c.promise},n=function(b,c){var d=a.defer();return i("GET",f+"/id/"+b+"?"+j({plain:c}),null,function(a){d.resolve(JSON.parse(a.body))}),d.promise},o=function(b,c,e,g,h){var k,l;return"undefined"==typeof c&&(c="inf"),"undefined"==typeof e&&(e="inf"),"undefined"==typeof g&&(g=null),k={uri:b||d,after:h,parent:g},"inf"!==c&&(k["limit"]=c),"inf"!==e&&(k["nested_limit"]=e),l=a.defer(),i("GET",f+"/?"+j(k),null,function(a){200===a.status?l.resolve(JSON.parse(a.body)):404===a.status?l.resolve({total_replies:0}):l.reject(a.body)}),l.promise},p=function(b){var c=a.defer();return i("POST",f+"/count",JSON.stringify(b),function(a){200===a.status?c.resolve(JSON.parse(a.body)):c.reject(a.body)}),c.promise},q=function(b){var c=a.defer();return i("POST",f+"/id/"+b+"/like",null,function(a){c.resolve(JSON.parse(a.body))}),c.promise},r=function(b){var c=a.defer();return i("POST",f+"/id/"+b+"/dislike",null,function(a){c.resolve(JSON.parse(a.body))}),c.promise},{endpoint:f,salt:c,create:k,modify:l,remove:m,view:n,fetch:o,count:p,like:q,dislike:r}}),define("app/dom",[],function(){"use strict";function a(a){this.obj=a,this.replace=function(b){var d=c.htmlify(b);return a.parentNode.replaceChild(d.obj,a),d},this.prepend=function(b){var d=c.htmlify(b);return a.insertBefore(d.obj,a.firstChild),d},this.append=function(b){var d=c.htmlify(b);return a.appendChild(d.obj),d},this.insertAfter=function(b){var d=c.htmlify(b);return a.parentNode.insertBefore(d.obj,a.nextSibling),d},this.on=function(b,c,d){a.addEventListener(b,function(a){c(a),(void 0===d||d)&&a.preventDefault()})},this.toggle=function(a,c,d){var e=new b(c,d);this.on(a,function(){e.next()})},this.detach=function(){return a.parentNode.removeChild(this.obj),this},this.remove=function(){a.parentNode.removeChild(this.obj)},this.show=function(){a.style.display="block"},this.hide=function(){a.style.display="none"},this.setText=function(b){a.textContent=b},this.setHtml=function(b){a.innerHTML=b},this.blur=function(){a.blur()},this.focus=function(){a.focus()},this.scrollIntoView=function(b){a.scrollIntoView(b)},this.setAttribute=function(b,c){a.setAttribute(b,c)},this.getAttribute=function(b){return a.getAttribute(b)},this.classList=a.classList,Object.defineProperties(this,{textContent:{get:function(){return a.textContent},set:function(b){a.textContent=b}},innerHTML:{get:function(){return a.innerHTML},set:function(b){a.innerHTML=b}},value:{get:function(){return a.value},set:function(b){a.value=b}},placeholder:{get:function(){return a.placeholder},set:function(b){a.placeholder=b}}})}var b=function(a,b){this.state=!1,this.next=function(){this.state?(this.state=!1,b(this)):(this.state=!0,a(this))},this.wait=function(){this.state=!this.state}},c=function(b,c,d){"undefined"==typeof d&&(d=!0),c||(c=window.document),c instanceof a&&(c=c.obj);var e=[].slice.call(c.querySelectorAll(b),0);return 0===e.length?null:1===e.length&&d?new a(e[0]):(e=[].slice.call(e,0),e.map(function(b){return new a(b)}))};return c.htmlify=function(b){if(b instanceof a)return b;if(b instanceof window.Element)return new a(b);var d=c.new("div");return d.innerHTML=b,new a(d.firstChild)},c.new=function(a,b){var c=document.createElement(a.split(".")[0]);return a.split(".").slice(1).forEach(function(a){c.classList.add(a)}),["A","LINK"].indexOf(c.nodeName)>-1&&(c.href="#"),b||0===b||(b=""),["TEXTAREA","INPUT"].indexOf(c.nodeName)>-1?c.value=b:c.textContent=b,c},c.each=function(a,b){Array.prototype.forEach.call(document.getElementsByTagName(a),b)},c}),define("app/utils",["app/i18n"],function(a){"use strict";var i,b=function(a){return(document.cookie.match("(^|; )"+a+"=([^;]*)")||0)[2]},c=function(a,b,c){return c=c||"0",a+="",a.length>=b?a:new Array(b-a.length+1).join(c)+a},d=function(b,c){var e,f,g,d=(b.getTime()-c.getTime())/1e3;return(isNaN(d)||0>d)&&(d=0),e=Math.ceil(d/60),f=Math.ceil(e/60),g=Math.ceil(f/24),45>=d&&a.translate("date-now")||90>=d&&a.pluralize("date-minute",1)||45>=e&&a.pluralize("date-minute",e)||90>=e&&a.pluralize("date-hour",1)||22>=f&&a.pluralize("date-hour",f)||36>=f&&a.pluralize("date-day",1)||5>=g&&a.pluralize("date-day",g)||8>=g&&a.pluralize("date-week",1)||21>=g&&a.pluralize("date-week",Math.ceil(g/7))||45>=g&&a.pluralize("date-month",1)||345>=g&&a.pluralize("date-month",Math.ceil(g/30))||547>=g&&a.pluralize("date-year",1)||a.pluralize("date-year",Math.ceil(g/365.25))},e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},f=function(a){return String(a).replace(/[&<>"'\/]/g,function(a){return e[a]})},g=function(a){var b=document.createElement("div");return b.innerHTML=a.replace(/<div><br><\/div>/gi,"<br>").replace(/<div>/gi,"<br>").replace(/<br>/gi,"\n").replace(/&nbsp;/gi," "),b.textContent.trim()},h=function(a){return a=f(a),a.replace(/\n\n/gi,"<br><div><br></div>").replace(/\n/gi,"<br>")};try{localStorage.setItem("x","y"),localStorage.removeItem("x"),i=localStorage}catch(j){i=function(a){return{setItem:function(b,c){a[b]=c},getItem:function(b){return"undefined"!=typeof a[b]?a[b]:null},removeItem:function(b){delete a[b]}}}({})}return{cookie:b,pad:c,ago:d,text:g,detext:h,localStorageImpl:i}}),function(a){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define("libjs-jade-runtime",[],a);else{var b;b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,b.jade=a()}}(function(){return function d(a,b,c){function e(g,h){var i,j,k;if(!b[g]){if(!a[g]){if(i="function"==typeof require&&require,!h&&i)return i(g,!0);if(f)return f(g,!0);throw j=new Error("Cannot find module '"+g+"'"),j.code="MODULE_NOT_FOUND",j}k=b[g]={exports:{}},a[g][0].call(k.exports,function(b){var c=a[g][1][b];return e(c?c:b)},k,k.exports,d,a,b,c)}return b[g].exports}var g,f="function"==typeof require&&require;for(g=0;g<c.length;g++)e(c[g]);return e}({1:[function(a,b,c){"use strict";function d(a){return null!=a&&""!==a}function e(a){return(Array.isArray(a)?a.map(e):a&&"object"==typeof a?Object.keys(a).filter(function(b){return a[b]}):[a]).filter(d).join(" ")}function h(a){return f[a]||a}function i(a){var b=String(a).replace(g,h);return b===""+a?a:b}var f,g;c.merge=function j(a,b){var c,e,f,g,h;if(1===arguments.length){for(c=a[0],e=1;e<a.length;e++)c=j(c,a[e]);return c}f=a["class"],g=b["class"],(f||g)&&(f=f||[],g=g||[],Array.isArray(f)||(f=[f]),Array.isArray(g)||(g=[g]),a["class"]=f.concat(g).filter(d));for(h in b)"class"!=h&&(a[h]=b[h]);return a},c.joinClasses=e,c.cls=function(a,b){var f,g,d=[];for(f=0;f<a.length;f++)b&&b[f]?d.push(c.escape(e([a[f]]))):d.push(e(a[f]));return g=e(d),g.length?' class="'+g+'"':""},c.style=function(a){return a&&"object"==typeof a?Object.keys(a).map(function(b){return b+":"+a[b]}).join(";"):a},c.attr=function(a,b,d,e){return"style"===a&&(b=c.style(b)),"boolean"==typeof b||null==b?b?" "+(e?a:a+'="'+a+'"'):"":0==a.indexOf("data")&&"string"!=typeof b?(-1!==JSON.stringify(b).indexOf("&")&&console.warn("Since Jade 2.0.0, ampersands (`&`) in data attributes will be escaped to `&amp;`"),b&&"function"==typeof b.toISOString&&console.warn("Jade will eliminate the double quotes around dates in ISO form after 2.0.0")," "+a+"='"+JSON.stringify(b).replace(/'/g,"&apos;")+"'"):d?(b&&"function"==typeof b.toISOString&&console.warn("Jade will stringify dates in ISO form after 2.0.0")," "+a+'="'+c.escape(b)+'"'):(b&&"function"==typeof b.toISOString&&console.warn("Jade will stringify dates in ISO form after 2.0.0")," "+a+'="'+b+'"')},c.attrs=function(a,b){var g,h,i,d=[],f=Object.keys(a);if(f.length)for(g=0;g<f.length;++g)h=f[g],i=a[h],"class"==h?(i=e(i))&&d.push(" "+h+'="'+i+'"'):d.push(c.attr(h,i,!1,b));return d.join("")},f={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"},g=/[&<>"]/g,c.escape=i,c.rethrow=function n(b,c,d,e){var g,h,i,j;if(!(b instanceof Error))throw b;if(!("undefined"==typeof window&&c||e))throw b.message+=" on line "+d,b;try{e=e||a("fs").readFileSync(c,"utf8")}catch(f){n(b,null,d)}throw g=3,h=e.split("\n"),i=Math.max(d-g,0),j=Math.min(h.length,d+g),g=h.slice(i,j).map(function(a,b){var c=b+i+1;return(c==d?"  > ":"    ")+c+"| "+a}).join("\n"),b.path=c,b.message=(c||"Jade")+":"+d+"\n"+g+"\n\n"+b.message,b},c.DebugItem=function(a,b){this.lineno=a,this.filename=b}},{fs:2}],2:[function(){},{}]},{},[1])(1)}),define("jade",{load:function(a){throw new Error("Dynamic load not allowed: "+a)}}),define("jade!app/text/postbox",function(){var a=function(a){var d,b=[],e=a||{};return function(a,c,e,f){b.push('<div class="isso-postbox"><div class="form-wrapper"><div class="textarea-wrapper"><div contenteditable="true" class="textarea placeholder">'+jade.escape(null==(d=e("postbox-text"))?"":d)+'</div></div><section class="auth-section"><p class="input-wrapper"><input type="text" name="author"'+jade.attr("placeholder",e("postbox-author"),!0,!1)+jade.attr("value",null!==a?""+a:"",!0,!1)+'/></p><p class="input-wrapper"><input type="email" name="email"'+jade.attr("placeholder",e("postbox-email"),!0,!1)+jade.attr("value",null!=c?""+c:"",!0,!1)+'/></p><p class="input-wrapper"><input type="text" name="website"'+jade.attr("placeholder",e("postbox-website"),!0,!1)+jade.attr("value",null!=f?""+f:"",!0,!1)+'/></p><p class="post-action"><input type="submit"'+jade.attr("value",e("postbox-submit"),!0,!1)+"/></p></section></div></div>")
}.call(this,"author"in e?e.author:"undefined"!=typeof author?author:void 0,"email"in e?e.email:"undefined"!=typeof email?email:void 0,"i18n"in e?e.i18n:"undefined"!=typeof i18n?i18n:void 0,"website"in e?e.website:"undefined"!=typeof website?website:void 0),b.join("")};return a}),define("jade!app/text/comment",function(){var a=function(a){var d,b=[],e=a||{};return function(a,c,e,f,g,h,i){b.push("<div"+jade.attr("id","isso-"+c.id,!0,!1)+' class="isso-comment">'),e.avatar&&b.push('<div class="avatar"><svg'+jade.attr("data-hash",""+c.hash,!0,!1)+"></svg></div>"),b.push('<div class="text-wrapper"><div role="meta" class="isso-comment-header">'),a(c.website)?b.push("<a"+jade.attr("href",""+c.website,!0,!1)+' rel="nofollow" class="author">'+jade.escape(null==(d=a(c.author)?c.author:h("comment-anonymous"))?"":d)+"</a>"):b.push('<span class="author">'+jade.escape(null==(d=a(c.author)?c.author:h("comment-anonymous"))?"":d)+"</span>"),b.push('<span class="spacer">&bull;</span><a'+jade.attr("href","#isso-"+c.id,!0,!1)+' class="permalink"><time'+jade.attr("title",""+g(c.created),!0,!1)+jade.attr("datetime",""+f(c.created),!0,!1)+'></time></a><span class="note">'+jade.escape(null==(d=2==c.mode?h("comment-queued"):4==c.mode?h("comment-deleted"):"")?"":d)+'</span></div><div class="text">'),4==c.mode?b.push("<p>&nbsp;</p>"):b.push(null==(d=c.text)?"":d),b.push('</div><div class="isso-comment-footer">'),e.vote&&b.push('<a href="#" class="upvote">'+(null==(d=i["arrow-up"])?"":d)+'</a><span class="spacer">|</span><a href="#" class="downvote">'+(null==(d=i["arrow-down"])?"":d)+"</a>"),b.push('<a href="#" class="reply">'+jade.escape(null==(d=h("comment-reply"))?"":d)+'</a><a href="#" class="edit">'+jade.escape(null==(d=h("comment-edit"))?"":d)+'</a><a href="#" class="delete">'+jade.escape(null==(d=h("comment-delete"))?"":d)+'</a></div><div class="isso-follow-up"></div></div></div>')}.call(this,"bool"in e?e.bool:"undefined"!=typeof bool?bool:void 0,"comment"in e?e.comment:"undefined"!=typeof comment?comment:void 0,"conf"in e?e.conf:"undefined"!=typeof conf?conf:void 0,"datetime"in e?e.datetime:"undefined"!=typeof datetime?datetime:void 0,"humanize"in e?e.humanize:"undefined"!=typeof humanize?humanize:void 0,"i18n"in e?e.i18n:"undefined"!=typeof i18n?i18n:void 0,"svg"in e?e.svg:"undefined"!=typeof svg?svg:void 0),b.join("")};return a}),define("jade!app/text/comment-loader",function(){var a=function(a){var d,b=[],e=a||{};return function(a,c){b.push("<div"+jade.attr("id","isso-loader-"+a.name,!0,!1)+' class="isso-comment-loader"><a href="#" class="load_hidden">'+jade.escape(null==(d=c("comment-hidden",a.hidden_replies))?"":d)+"</a></div>")}.call(this,"comment"in e?e.comment:"undefined"!=typeof comment?comment:void 0,"pluralize"in e?e.pluralize:"undefined"!=typeof pluralize?pluralize:void 0),b.join("")};return a}),define("app/jade",["libjs-jade-runtime","app/utils","jade!app/text/postbox","jade!app/text/comment","jade!app/text/comment-loader"],function(runtime,utils,tt_postbox,tt_comment,tt_comment_loader){"use strict";var globals={},templates={},load=function(name,js){templates[name]=function(jade){var fn;return eval("fn = "+js),fn}(runtime)},set=function(a,b){globals[a]=b};return load("postbox",tt_postbox),load("comment",tt_comment),load("comment-loader",tt_comment_loader),set("bool",function(a){return a?!0:!1}),set("humanize",function(a){return"object"!=typeof a&&(a=new Date(1e3*parseInt(a,10))),a.toString()}),set("datetime",function(a){return"object"!=typeof a&&(a=new Date(1e3*parseInt(a,10))),[a.getUTCFullYear(),utils.pad(a.getUTCMonth(),2),utils.pad(a.getUTCDay(),2)].join("-")+"T"+[utils.pad(a.getUTCHours(),2),utils.pad(a.getUTCMinutes(),2),utils.pad(a.getUTCSeconds(),2)].join(":")+"Z"}),{set:set,render:function(a,b){var c,e,f,g,d=templates[a];if(!d)throw new Error("Template not found: '"+a+"'");b=b||{},e=[];for(f in b)b.hasOwnProperty(f)&&!globals.hasOwnProperty(f)&&(e.push(f),globals[f]=b[f]);for(c=templates[a](globals),g=0;g<e.length;g++)delete globals[e[g]];return c}}}),define("app/lib/editor",["app/dom","app/i18n"],function(a,b){"use strict";return function(c){return c=a.htmlify(c),c.setAttribute("contentEditable",!0),c.on("focus",function(){c.classList.contains("placeholder")&&(c.innerHTML="",c.classList.remove("placeholder"))}),c.on("blur",function(){0===c.textContent.length&&(c.textContent=b.translate("postbox-text"),c.classList.add("placeholder"))}),c}}),define("app/lib/identicons",["app/lib/promise","app/config"],function(a,b){"use strict";var c=5,d=function(a,b){return a.length>=b?a:new Array(b-a.length+1).join("0")+a},e=function(a,b,c,d,e,f){var g=document.createElementNS("http://www.w3.org/2000/svg","rect");g.setAttribute("x",d+b*e),g.setAttribute("y",d+c*e),g.setAttribute("width",e),g.setAttribute("height",e),g.setAttribute("style","fill: "+f),a.appendChild(g)},f=function(f,g,h){var i=document.createElementNS("http://www.w3.org/2000/svg","svg");return i.setAttribute("version","1.1"),i.setAttribute("viewBox","0 0 "+h+" "+h),i.setAttribute("preserveAspectRatio","xMinYMin meet"),i.setAttribute("shape-rendering","crispEdges"),e(i,0,0,0,h+2*g,b["avatar-bg"]),null===typeof f?i:(a.when(f,function(a){var j,k,l,m,f=d((parseInt(a,16)%Math.pow(2,18)).toString(2),18),h=0;for(i.setAttribute("data-hash",a),j=parseInt(f.substring(f.length-3,f.length),2),k=b["avatar-fg"][j%b["avatar-fg"].length],l=0;l<Math.ceil(c/2);l++)for(m=0;c>m;m++)"1"===f.charAt(h)&&(e(i,l,m,g,8,k),l<Math.floor(c/2)&&e(i,c-1-l,m,g,8,k)),h++}),i)},g=function(a,b){var c=parseInt([0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,0].join(""),2).toString(16),d=f(c,a,b);return d.setAttribute("className","blank"),d};return{generate:f,blank:g}}),define("app/lib",["require","app/lib/editor","app/lib/identicons"],function(a){return{editorify:a("app/lib/editor"),identicons:a("app/lib/identicons")}}),define("app/isso",["app/dom","app/utils","app/config","app/api","app/jade","app/i18n","app/lib","app/globals"],function(a,b,c,d,e,f,g,h){"use strict";var i=function(f){var h=b.localStorageImpl,i=a.htmlify(e.render("postbox",{author:JSON.parse(h.getItem("author")),email:JSON.parse(h.getItem("email")),website:JSON.parse(h.getItem("website"))}));return i.onsuccess=function(){},i.validate=function(){return b.text(a(".textarea",this).innerHTML).length<3||a(".textarea",this).classList.contains("placeholder")?(a(".textarea",this).focus(),!1):c["require-email"]&&a("[name='email']",this).value.length<=0?(a("[name='email']",this).focus(),!1):c["require-author"]&&a("[name='author']",this).value.length<=0?(a("[name='author']",this).focus(),!1):!0},c["require-email"]&&(a("[name='email']",i).placeholder=a("[name='email']",i).placeholder.replace(/ \(.*\)/,"")),c["require-author"]&&(a("[name='author']",i).placeholder=a("[name='author']",i).placeholder.replace(/ \(.*\)/,"")),a("[type=submit]",i).on("click",function(){if(i.validate()){var c=a("[name=author]",i).value||null,e=a("[name=email]",i).value||null,g=a("[name=website]",i).value||null;h.setItem("author",JSON.stringify(c)),h.setItem("email",JSON.stringify(e)),h.setItem("website",JSON.stringify(g)),d.create(a("#isso-thread").getAttribute("data-isso-id"),{author:c,email:e,website:g,text:b.text(a(".textarea",i).innerHTML),parent:f||null,title:a("#isso-thread").getAttribute("data-title")||null}).then(function(b){a(".textarea",i).innerHTML="",a(".textarea",i).blur(),k(b,!0),null!==f&&i.onsuccess()})}}),g.editorify(a(".textarea",i)),i},j=function(b,f){var g,h;null===b.id?(g=a("#isso-root"),b.name="null"):(g=a("#isso-"+b.id+" > .text-wrapper > .isso-follow-up"),b.name=b.id),h=a.htmlify(e.render("comment-loader",{comment:b})),g.append(h),a("a.load_hidden",h).on("click",function(){h.remove(),d.fetch(a("#isso-thread").getAttribute("data-isso-id"),c["reveal-on-click"],c["max-comments-nested"],b.id,f).then(function(a){if(0!==a.total_replies){var b=0;a.replies.forEach(function(a){k(a,!1),a.created>b&&(b=a.created)}),a.hidden_replies>0&&j(a,b)}},function(a){console.log(a)})})},k=function(l,m){var p,q,r,s,t,u,v,w,x,y,n=a.htmlify(e.render("comment",{comment:l})),o=function(){a(".permalink > time",n).textContent=b.ago(h.offset.localTime(),new Date(1e3*parseInt(l.created,10))),setTimeout(o,6e4)};o(),c["avatar"]&&a("div.avatar > svg",n).replace(g.identicons.generate(l.hash,4,48)),p=null===l.parent?a("#isso-root"):a("#isso-"+l.parent+" > .text-wrapper > .isso-follow-up"),p.append(n),m&&n.scrollIntoView(),q=a("#isso-"+l.id+" > .text-wrapper > .isso-comment-footer"),r=a("#isso-"+l.id+" > .text-wrapper > .isso-comment-header"),s=a("#isso-"+l.id+" > .text-wrapper > .text"),t=null,a("a.reply",q).toggle("click",function(b){t=q.insertAfter(new i(null===l.parent?l.id:l.parent)),t.onsuccess=function(){b.next()},a(".textarea",t).focus(),a("a.reply",q).textContent=f.translate("comment-close")},function(){t.remove(),a("a.reply",q).textContent=f.translate("comment-reply")}),c.vote&&(u=c["vote-levels"],"string"==typeof u&&(u=u.split(",")),v=function(b){var d,e,c=a("span.votes",q);if(null===c?q.prepend(a.new("span.votes",b)):c.textContent=b,b?n.classList.remove("isso-no-votes"):n.classList.add("isso-no-votes"),u)for(d=!0,e=0;e<=u.length;e++)d&&(e>=u.length||b<u[e])?(n.classList.add("isso-vote-level-"+e),d=!1):n.classList.remove("isso-vote-level-"+e)},a("a.upvote",q).on("click",function(){d.like(l.id).then(function(a){v(a.likes-a.dislikes)})}),a("a.downvote",q).on("click",function(){d.dislike(l.id).then(function(a){v(a.likes-a.dislikes)})}),v(l.likes-l.dislikes)),a("a.edit",q).toggle("click",function(e){var h=a("a.edit",q),i=c["avatar"]?a(".avatar",n,!1)[0]:null;h.textContent=f.translate("comment-save"),h.insertAfter(a.new("a.cancel",f.translate("comment-cancel"))).on("click",function(){e.canceled=!0,e.next()}),e.canceled=!1,d.view(l.id,1).then(function(c){var d=g.editorify(a.new("div.textarea"));d.innerHTML=b.detext(c.text),d.focus(),s.classList.remove("text"),s.classList.add("textarea-wrapper"),s.textContent="",s.append(d)}),null!==i&&i.hide()},function(e){var g=a(".textarea",s),h=c["avatar"]?a(".avatar",n,!1)[0]:null;if(e.canceled||null===g)s.innerHTML=l.text;else{if(b.text(g.innerHTML).length<3)return g.focus(),e.wait(),void 0;d.modify(l.id,{text:b.text(g.innerHTML)}).then(function(a){s.innerHTML=a.text,l.text=a.text})}s.classList.remove("textarea-wrapper"),s.classList.add("text"),null!==h&&h.show(),a("a.cancel",q).remove(),a("a.edit",q).textContent=f.translate("comment-edit")}),a("a.delete",q).toggle("click",function(b){var c=a("a.delete",q),d=!b.state;c.textContent=f.translate("comment-confirm"),c.on("mouseout",function(){c.textContent=f.translate("comment-delete"),b.state=d,c.onmouseout=null})},function(){var b=a("a.delete",q);d.remove(l.id).then(function(c){c?n.remove():(a("span.note",r).textContent=f.translate("comment-deleted"),s.innerHTML="<p>&nbsp;</p>",a("a.edit",q).remove(),a("a.delete",q).remove()),b.textContent=f.translate("comment-delete")})}),w=function(c){b.cookie("isso-"+l.id)?setTimeout(function(){w(c)},15e3):null!==a(c,q)&&a(c,q).remove()},w("a.edit"),w("a.delete"),x=function(a){b.cookie("isso-"+l.id)?setTimeout(function(){x(a)},15e3):q.append(a)},!c["reply-to-self"]&&b.cookie("isso-"+l.id)&&x(a("a.reply",q).detach()),l.hasOwnProperty("replies")&&(y=0,l.replies.forEach(function(a){k(a,!1),a.created>y&&(y=a.created)}),l.hidden_replies>0&&j(l,y))};return{insert:k,insert_loader:j,Postbox:i}}),define("app/count",["app/api","app/dom","app/i18n"],function(a,b,c){return function(){var e,d={};b.each("a",function(a){if(a.href.match(/#isso-thread$/)){var b=a.getAttribute("data-isso-id")||a.href.match(/^(.+)#isso-thread$/)[1].replace(/^.*\/\/[^\/]+/,"");b in d?d[b].push(a):d[b]=[a]}}),e=Object.keys(d),a.count(e).then(function(a){var b,f,g;for(b in d)if(d.hasOwnProperty(b))for(f=e.indexOf(b),g=0;g<d[b].length;g++)d[b][g].textContent=c.pluralize("num-comments",a[f])})}}),define("text",{load:function(a){throw new Error("Dynamic load not allowed: "+a)}}),define("text!app/../../css/isso.css",[],function(){return'.isso-postbox .textarea-wrapper{margin-bottom:1rem}.isso-postbox .textarea-wrapper .textarea{min-height:8em}.isso-postbox .auth-section .input-wrapper input,.isso-postbox .textarea-wrapper .textarea{background:#fafafa;border:1px solid #eee;border-radius:.2rem;padding:.6rem .8rem}.isso-postbox .auth-section .input-wrapper input:focus,.isso-postbox .textarea-wrapper .textarea:focus{border-color:#999;outline:0}.isso-postbox .auth-section{display:flex;display:-ms-flexbox}.isso-postbox .auth-section .input-wrapper{-ms-flex:1 1 25%;flex:1 1 25%;padding-right:2rem;width:25%}.isso-postbox .auth-section .post-action{-ms-flex:1 1 25%;flex:1 1 25%;width:25%;margin-right:1.5rem;}.isso-postbox .auth-section .post-action input{-webkit-appearance:none;-moz-appearance:none;appearance:none;background:#2687fb;border:.1rem solid #177ffb;border-radius:.2rem;color:#fff;padding:.6rem}.isso-postbox .auth-section .post-action input:focus{background:#177ffb;outline:0}.isso-postbox .auth-section input{width:100%}.isso-comment{display:flex;display:-ms-flexbox;margin-top:2rem}.isso-comment .avatar{margin-right:1.0rem}.isso-comment .avatar svg{border-radius:50%;height:auto;width:3.0rem}.isso-comment .isso-comment-header .author{font-weight:bolder}.isso-comment .isso-comment-header .spacer{color:#ccc;margin-left:1rem;margin-right:1rem}.isso-comment .isso-comment-header .permalink{color:#ccc}.isso-comment .isso-comment-header .note{margin-left:.5rem}.isso-thread{font-family:sans-serif;line-height:1.15;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}.isso-thread section{display:block}.isso-thread a{background-color:transparent;-webkit-text-decoration-skip:objects}.isso-thread input{font-family:inherit;font-size:inherit;line-height:inherit;margin:0}.isso-thread input{overflow:visible}[type=submit]{-webkit-appearance:button}[type=submit]::-moz-focus-inner{border-style:none;padding:0}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}*,::after,::before{box-sizing:inherit}.isso-thread{background:#fff;color:#404040;font-family:-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",sans-serif;font-size:1.4rem;overflow-x:hidden;text-rendering:optimizeLegibility}.isso-thread p{line-height:2.4rem;margin:0 0 1rem}.form-input:not(:placeholder-shown):invalid{border-color:#e85600}.form-input:not(:placeholder-shown):invalid:focus{box-shadow:0 0 0 .2rem rgba(232,86,0,.15)}.isso-thread .avatar{background:#2687fb;border-radius:50%;color:rgba(255,255,255,.85);display:inline-block;font-size:1.4rem;font-weight:300;height:3.2rem;line-height:1;margin:0;position:relative;vertical-align:middle;width:3.2rem}'}),define("app/text/css",["text!../../../css/isso.css"],function(a){return{inline:a}}),define("text!app/text/arrow-down.svg",[],function(){return'<!-- Generator: IcoMoon.io --><svg width="16" height="16" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="gray">\n  <g>\n    <path d="M 24.773,13.701c-0.651,0.669-7.512,7.205-7.512,7.205C 16.912,21.262, 16.456,21.44, 16,21.44c-0.458,0-0.914-0.178-1.261-0.534 c0,0-6.861-6.536-7.514-7.205c-0.651-0.669-0.696-1.87,0-2.586c 0.698-0.714, 1.669-0.77, 2.522,0L 16,17.112l 6.251-5.995 c 0.854-0.77, 1.827-0.714, 2.522,0C 25.47,11.83, 25.427,13.034, 24.773,13.701z">\n    </path>\n  </g>\n</svg>\n'}),define("text!app/text/arrow-up.svg",[],function(){return'<!-- Generator: IcoMoon.io --><svg width="16" height="16" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="gray">\n  <g>\n    <path d="M 24.773,18.299c-0.651-0.669-7.512-7.203-7.512-7.203C 16.912,10.739, 16.456,10.56, 16,10.56c-0.458,0-0.914,0.179-1.261,0.536 c0,0-6.861,6.534-7.514,7.203c-0.651,0.669-0.696,1.872,0,2.586c 0.698,0.712, 1.669,0.77, 2.522,0L 16,14.89l 6.251,5.995 c 0.854,0.77, 1.827,0.712, 2.522,0C 25.47,20.17, 25.427,18.966, 24.773,18.299z">\n    </path>\n  </g>\n</svg>\n'}),define("app/text/svg",["text!./arrow-down.svg","text!./arrow-up.svg"],function(a,b){return{"arrow-down":a,"arrow-up":b}}),require(["app/lib/ready","app/config","app/i18n","app/api","app/isso","app/count","app/dom","app/text/css","app/text/svg","app/jade"],function(a,b,c,d,e,f,g,h,i,j){"use strict";j.set("conf",b),j.set("i18n",c.translate),j.set("pluralize",c.pluralize),j.set("svg",i),a(function(){if(b["css"]){var a=g.new("style");a.type="text/css",a.textContent=h.inline,g("head").append(a)}return f(),null===g("#isso-thread")?console.log("abort, #isso-thread is missing"):(g("#isso-thread").append(g.new("h4")),g("#isso-thread").append(new e.Postbox(null)),g("#isso-thread").append('<div id="isso-root"></div>'),d.fetch(g("#isso-thread").getAttribute("data-isso-id"),b["max-comments-top"],b["max-comments-nested"]).then(function(a){var b,d;return 0===a.total_replies?(g("#isso-thread > h4").textContent=c.translate("no-comments"),void 0):(b=0,d=a.total_replies,a.replies.forEach(function(a){e.insert(a,!1),a.created>b&&(b=a.created),d+=a.total_replies}),g("#isso-thread > h4").textContent=c.pluralize("num-comments",d),a.hidden_replies>0&&e.insert_loader(a,b),window.location.hash.length>0&&g(window.location.hash).scrollIntoView(),void 0)},function(a){console.log(a)}),void 0)})}),define("embed",function(){})}();