# when running hexo in a subdirectory (e.g. domain.tld/blog), remove leading slashes ( "/archives" -> "archives" )
menu:
  home: /
  #categories: /categories
  archives: /archives
  #tags: /tags
  #commonweal: /404.html
  guestbook: /guestbook
  about: /about

# Place your favicon.ico to /source directory.
favicon: /favicon.ico

# Set default keywords (Use a comma to separate)
keywords: "Android核心技术, 插件化, react-native, 插件框架, android, plugin, framework"

# Set rss to false to disable feed link.
# Leave rss as empty to use site's feed link.
# Set rss to specific value if you have burned your feed already.
rss:

# Icon fonts
# Place your font into next/source/fonts, specify directory-name and font-name here
# Avialable: default | linecons | fifty-shades | feather
icon_font: default
#icon_font: fifty-shades
#icon_font: feather
#icon_font: linecons

# Code Highlight theme
# Available value: normal | night | night eighties | night blue | night bright
# https://github.com/chriskempson/tomorrow-theme
highlight_theme: normal


# MathJax Support
mathjax:


# Schemes
scheme: Mist


# Sidebar, available value:
#  - post    expand on posts automatically. Default.
#  - always  expand for all pages automatically
#  - hide    expand only when click on the sidebar toggle icon.
sidebar: hide
#sidebar: always
#sidebar: hide


# Automatically scroll page to section which is under <!-- more --> mark.
scroll_to_more: false


# Automatically add list number to toc.
toc_list_number: true

# Automatically Excerpt
auto_excerpt:
  enable: false
  length: 350

# Use Lato font
# Note: this option is avialable only when the language is not `zh-Hans`
use_font_lato: true

# Make duoshuo show UA
# user_id must NOT be null when admin_enable is true!
# you can visit http://dev.duoshuo.com get duoshuo user id.
duoshuo_info:
  ua_enable: true
  admin_enable: false
  user_id: 0
  #admin_nickname: ROOT

# youyan
isso_ip: https://isso.dimenspace.com

## DO NOT EDIT THE FOLLOWING SETTINGS
## UNLESS YOU KNOW WHAT YOU ARE DOING

# Use velocity to animate everything.
use_motion: true

# Fancybox
fancybox: true

# Static files
vendors: vendors
css: css
js: js
images: images

# Theme version
version: *******

# donate
donate: true
donate_message: 免费分享，随意打赏 ^ ^
