title: mac下Android studio快捷键配置
date: 2015-10-02 14:17:47
tags: 
- ideavim
---

前两天重装了mac系统；之前的配置都丢了，因此纪录一下配置的过程以防惨案再次发生～

快捷键为什么要配置？直接使用不就好！

这个事实基于我们使用vim模拟，IdeaVim；会有一些快捷键的冲突；然后使用HHKB的话，为了契合这个键盘，需要做一些额外的处理。

基于两个前提：
1. ideavim的某些ctrl开头的快捷键与IDE冲突，需要手动解决
2. 使用hhkb键盘，没有上下左右，没有`F1..Fn`系列不方便

<!-- more -->

### ideavim兼容
1. `ctrl + r`这个在vim模式下是重做，相对于`u`(undo)；对于重度vimer必不可少，虽然ide的重做也可以用；这么配置带来一个问题，那就是`ctrl + r`在IDE里面意思是`Run`，必须改键；以前习惯`shift + F10`因为windows这么配置的，但是在hhkb键盘，这个键位要摁三个键：`fn,shift,0`;二逼地配置为`shift + 0`之后，发现怎么也打不出`)`了；最终改为**`option + r`**很方便，也好记（run），不冲突。
2. `ctrl + a` 这个在Vim里面意思是光标所在的数字加一；IDE里面意思都是移到行首，等同于命令行模式下的`^`，既然选择Vim就用vim的移动方式；在插入模式下要多按键，为了避免换个IDE就傻缺，用Vim；另外IDE 的`cmd + ⬅️`也有这个意思。
3. `ctrl + b` IDE是往左移动光标；VIM是向上翻页；写代码要频繁用翻页，说明你得重构了；选IDE。`ctrl + f`同理（向下翻页）
4. `ctrl + e`  在vim里面意思是光标不动，屏幕滚动；在IDE里面是移到行尾，道理同`ctrl + a`选VIM；`cmd + ➡️`可以使用；
5. `ctrl + o` 在VIM里面意思是：在插入模式下执行命令然后回到插入模式；太有用了；比如你在插入模式，在括号里面写完了，想去行尾加个分号结束，咋办？用上下左右光标？No！你可以`ctrl + o A`多么优雅！不管IDE什么意思，必须选择Vim。IDE的意思是`override`这个必须用的，没办法改了；改成`ctrl + i`。
5. `ctrl + g` 在IDE里面在查找里面可以把下一个单词加入选择列表；用的情况比较少；在VIM里面，意思有两个：命令模式下等同于`:f`显示当前文件路径以及行数；插入模式下是一个前缀 ；`ctrl g k`等同于`ctrl + o gk` 所以还不错；选Vim。
6. `ctrl + i` IDE是实现方法（实际上改成了ctrl ＋ o的意思，不用担心这个包含前者。），很有用；VIm是光标前插入空格；选IDE；
7. `ctrl + p`以及`ctrl + n`这个是vim的自动完成；在IDE前面没啥用；IDE里面意思是光标移动，选IDE
8. `ctrl + T` 在IDE里面这个功能太好用了；重构用的；必须IDE
9. `ctrl + v` IDE里面是cvs的操作；好用；在Vim里面是进入可视化选择模式，可很有用，但是放心 还有一个快捷键`ctrl + q`是一样的功能；这个是为了兼容windows的；因为windows这个是粘贴～

好了，vim兼容就这么多。

然后配置一些HHKB不好按的键，比如Fn系列；没有这个键盘的话自行略过（真心推荐）；

### HHKB修改

1.  `cmd + shift + F12` ：隐藏所有工具栏，在需要安静写代码的时候很有用；我很常用；改成`ctrl + option + (加号)` 因为这个➕在hhkb里面对应F12；
2.  `cmd + F12` ： 显示文件的目录结构；太长用了，在类里面导航全靠这个；改为：` option + (加号)` 这个比上面那个常用；因此这样。
3.  `option + F7` : 查找谁用了它；超有用，没有之一；添加额外选项：`option + 7`
4.  `option + cmd + F7`: 类似上面的，但是在一个弹出框显示；个人觉得比上面那个好用；上面那个单独的窗口导致有时候切换有可能需要鼠标；不方便，考虑这两个功能对换键位；暂时加上`option + cmd + 7`
4.  `option + ⬆️`：类似windows下面的`ctrl + w` IDEA最有用的特性之一。但是在HHKB按方向键要两个键，改一下；另外，通常选择之后是复制，所以，最好要用cmd组合；索性改成`cmd + w`原来这个意思是关闭标签，没啥用；一般不自动关闭的。

另外一些需要用到Fn系列键位的，可以自行配置一下。
