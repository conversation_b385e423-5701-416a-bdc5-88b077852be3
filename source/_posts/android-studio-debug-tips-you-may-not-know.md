title: "Android Studio你不知道的调试技巧"
date: 2015-12-21 20:39:03
tags:
- android
categories:
- 实用技巧
---

写代码不可避免有Bug，通常情况下除了日志最直接的调试手段就是debug；那么你的调试技术停留在哪一阶段呢？仅仅是下个断点单步执行吗？或者你知道 `Evaluate Expression`, 知道条件断点；可是你听说过日志断点吗，`Method Breakpoint`, ```Exception Breakpoint``` 呢？还有高大上的 ```Field Watchpoint``` ?

<img src="http://booluimg.dimenspace.com/test/1450701212799.png" width="338" alt="几种不同的断点"/>

你有关注过Android Studio旁边断点的区别吗？比如上图三个断点有什么不同？且听我一一道来。
<!--more -->

## 调试基础

一般来说我们有两种办法调试一个debuggable的apk；其一是下好断点，然后用debug模式编译安装这个app；其二是 `attach process`，在Android Studio里面就是这么一个对话框：

<img src="http://booluimg.dimenspace.com/test/1450697931900.png" width="273" alt="Attach Process"/>

第二种方法比较常用，我们可以在启动apk之后，直接下断点，然后attach process到制定进程，条件触发之后就可以直接进入调试模式。

其他的一些单步执行，`step into`, `step out`, `force step into` 等就不提了；基本的跟踪手段。

还是提一下，下断点最简单的办法，是在代码编辑器的左侧，行号右边鼠标点击一下即可。

## Evaluate Expression
这个功能非常实用，可以在断点处直接进入一个求值环境，在这里你可以执行任何你感兴趣的表达式；如下图：

<img src="http://booluimg.dimenspace.com/test/1450698259155.png" width="524" alt="Evaluate Expression"/>

比如在断点处有一个对象`object`，如果你要查看它的某个属性很简单，在Debug窗口就能看到，但是如果你想要执行它的某个方法看看结果是什么呢？借助这个可以实现。当然它的功能远不止这么多，相当于直接进入了一个 `REPL`环境，非常实用。忘了说了，快捷键 `Alt + F8` :P

## 条件断点
假设你的断点在一个列表的循环里面，可是你只对这个列表的某一个元素感兴趣，只想在遇到这个元素的时候才断下来；你是一直人肉 `F9` 直到满足条件吗？条件断点就是满足这种需求的，顾名思义，在特定条件下的断点。使用起来也非常简单，在你的断点上鼠标右键会出现一个小窗口，写上条件即可。

<img src="http://booluimg.dimenspace.com/test/1450698641184.png" width="465" alt="条件断点"/>

## 日志断点
很多时候我们调试的时候更多的是打印日志定位异常代码，缩小范围之后再使用断点解决问题；所以经常做的事情就是在代码里面添加日志信息，输出函数参数，返回信息，输出我们感兴趣的变量信息等。

但是这么做一个问题就是，我们添加了日志代码需要重新编译；在没有 `Instant Run` 之前的黑暗时代这么做是非常痛苦的，每次编译少则几十秒，多则几分钟；这样无意义的等待简直就是折磨；其实，除了热部署工具，我们还可以使用日志断点解决这个问题。

首先我们在想要输出信息的地方下一个断点；然后右键这个断点，在出现的设置框里面把这个断点的 `suspend` 属性设置为 `False` ，这样虽然叫做“断点”，但是并不会真正断下来；然后，我们在 `log message` 里面填上我们想要输出的日志信息。如下图(注意标红位置）：

<img src="http://booluimg.dimenspace.com/test/1450699187057.png" width="591" alt="日志断点"/>

这样，每次代码执行到这个断点的位置，这个可爱的断点并不会使我们的程序停下来，而是输出我们告诉它的日志信息，然后继续执行；非常方便。

## 方法断点

传统的调试方式是以行为单位的，所谓单步调试；但是很多时候我们关心的是某个函数的参数，返回值；（回想一下我们使用日志的时候打印的最多的信息难道不是函数的参数和返回值吗？）使用方法断点，我们可以在函数级别进行调试；如果经常跳进跳出函数或者只对某个函数的参数感兴趣，这种类型的断点非常实用。具体使用方法有两种方式；最简单的是在你感兴趣的方法头那一行打上断点，这时候你会发现断点图标有点不一样，这就是方法断点了，如下图：

<img src="http://booluimg.dimenspace.com/test/1450699584560.png" width="503" alt="方法断点"/>

另外一种方式是通过*断点设置窗口*, 后面介绍。

## 异常断点
在有些情况下，我们只对某些特定的异常感兴趣，或者我们只对异常感兴趣；我们希望只要程序发生异常程序就能断下来；这好像保存现场一样，只要发生命案了（异常），第一时间保存现场，这样什么指纹之类的线索就会清晰很多，坏蛋就算想逃也是插翅难飞啊。

Android Studio给了我们这个能力！那就是**异常断点**!可以在特定异常发生的时候，直接让整个程序断下来；如果你对所有异常感兴趣，直接 `Throwable` 即可。

具体做法是，进入 `Run -> View BreakPoints` 或者使用快捷键打开断点设置窗口；如下图：

<img src="http://booluimg.dimenspace.com/test/1450700136670.png" width="273" alt="断点设置窗口" />

点击左上角的 ➕ ，会出现一个选择框；选择`Exception Breakpoint`；然后会出现一个对话框，选择你感兴趣的异常：

<img src="http://booluimg.dimenspace.com/test/1450700226518.png" width="526" alt="异常断点" />

## Field WatchPoint
在上面我们添加异常断点的时候，点击加号的时候，有四个选项；第一个就是我们前面所说的第二种方法断点的添加方式，第三个是异常断点，那么第二个 **Field WatchPoint** 是干什么的呢？

有没有这样一种场景：你发现某个值莫名其妙滴不知道什么时候被谁给修改了，罪魁祸首是谁？Java虽然是值传递，但是引用也可以是值；对象全部存放在堆上面，而堆是被所有线程共享的，因此在非常复杂的场景下，你根本不知道这些共享变量被谁修改了，这样非常危险；在多线程环境下，不变性是一个很重要的特性，我们看到高并发的语言诸如 `Erlang`, `Scala` 对于这种不变性都有着某种程度的支持。

好吧，扯远了；那么我们怎么揪出这个修改我们值的捣蛋鬼呢？那就是这个 **Field WatchPoint**的功能了；使用它我们可以在某个**Field**被访问或者修改的时候让程序断下来；完美解决这个问题。

下断点的方式和方法断点类似，也有两种；第一种是直接在某个字段的声明处下断点，这时候断点图标会改变，如下图：

<img src="http://booluimg.dimenspace.com/test/1450700886216.png" width="354" alt="Field WatchPoint" />

右键这个断点我们可以进行一些设置，比如默认是被修改的时候断下来，你也可以改为每次访问这个字段就断下来。

另外一种方式是 `Run -> View BreakPoint` 打开设置，与异常断点类似。

## 远不止这么多

上面介绍了这么多给力的功能，其实还有很多细节；打开断点设置窗口（Run -> View Breakpoint`):

<img src="http://booluimg.dimenspace.com/test/1450701055366.png" width="873"/>

我们可以对**感兴趣的类，感兴趣的某个特定对象**下断点，也可以设置断点的次数，还能使断点在特定的线程才断下来；这些细节就不详细介绍了，大家自己去发掘！

Have Fun!!
