title: "技术人员的桎梏"
date: 2023-11-10 18:05:54

---

前几天我发了一篇文章：[小米的存储扩容是拆承重墙吗](https://mp.weixin.qq.com/s/F23K1HHvau0WqCfVBmHE9w)，文中站在非技术角度解读，其核心论点是“小米公司不可能背着巨大的风险去做收效甚微的事”，有粉丝表示赞同：“当对技术不了解的时候，还可以从基本逻辑来理解”；然而有一些童鞋嗤之以鼻，表示没有技术分析，纯属 YY。

我观察了这些粉丝过往的留言，发现他们大部分都是懂技术的，其专业程度远超普通用户；我对此表示理解，他们就是因为我的技术关注我的，现在我在这说别的，当然属于是在扯淡。的确，我做技术已经十年了，在某些领域做得还不错，这自然会给人一种固有的刻板印象；五年前，我也因为技术而从大厂出来选择自主创业；不过，我对技术的理解并非你们想象中的那样。

**技术，只是解决问题的一种方式**。它是你日常工具箱里面的一种工具，就像水管松了用扳手拧，灯泡不亮用万用表测，衣服破了用缝纫机补一样。

<!-- more -->

然而，很多技术人员总喜欢尝试用技术解释一切，误认为技术能够解决所有问题。我上面举的例子可能更偏工程技术一点，但即使是科学技术也只能解决特定领域的问题，甚至科学本身也是有范畴的。打个比方，你能用技术解决婆媳矛盾吗？你能用技术解决巴以冲突？你能用技术让 Google 进入中国？很显然不能，甚至你邻居家小孩天天在门口拉屎都用技术解决不了。这种“拿着锤子看什么都像是钉子”的问题，并非只有技术人员才有，但越优秀的技术人员，往往越容易陷入这个怪圈。

技术人员还有一个特点，因为他们懂技术，动手能力强，遇到问题往往喜欢自己折腾，除非实在搞不定，绝对不会交给别人。我在网上见到太多的人，它们自己亲自注册公司，自己去给自己软件申请著作权，自己去开通 ChatGPT 账号... 事实上呢，这些事情花钱请别人都能搞定，而且不需要花很多额外的钱。这种“花钱买别人的时间”就是典型的非技术思维，你花钱请别人做给你自己省下来的时间，如果你能创造更多的价值，那干嘛要自己做呢？有人说，我的时间不值钱，让别人做纯属亏本；那你能不能想想怎么把自己时间变得更值钱？

我知道我今天的这两个观点在技术人眼中大逆不道，但我还是要讲出来；我粉丝的绝大部分都是年轻人，很多都在读书还有的刚步入社会；我们在学校的时候，老师只会教我们“好好读书，学好知识（技术）”，但是技术只是一种解决问题的工具，当我们步入社会，会遇到很多技术无法解决的问题；所以越早意识到这一点，主动培养自己技术之外的能力，你的生活会变得越轻松。很遗憾我当年毕业的时候没有人告诉我这一点，因此我作为一个愣头青愣了很多年；我没有想要让你们认同我的观点，把它当作维叔的一个友情提醒就行了。

十年前我花了半个月的工资买了一个自定义域名 `weishu.me`，并用它打造了自己的技术博客，博客中有一句座右铭：“不为繁华易匠心”，今天我想把它改成：“君子不器”。