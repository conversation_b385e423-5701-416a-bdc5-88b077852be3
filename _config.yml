# Hexo Configuration
## Docs: http://hexo.io/docs/configuration.html
## Source: https://github.com/hexojs/hexo/

# Site
title: <PERSON><PERSON>'s Notes
subtitle: 
description: 为数不多的维术
author: weishu
avatar: /images/avatar.jpg
language: zh-Hans
timezone:

# URL
## If your site is put in a subdirectory, set url as 'http://yoursite.com/child' and root as '/child/'
url: http://weishu.me
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:

# Directory
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# Writing
new_post_name: :title.md # File name of new posts
default_layout: post
titlecase: false # Transform title into titlecase
external_link: true # Open external links in new tab
filename_case: 0
render_drafts: false
post_asset_folder: false
relative_link: false
future: true
highlight:
  enable: true
  line_number: true
  auto_detect: true
  tab_replace:

# Category & Tag
default_category: uncategorized
category_map:
tag_map:

# Date / Time format
## Hexo uses Moment.js to parse and display date
## You can customize the date format as defined in
## http://momentjs.com/docs/#/displaying/format/
date_format: YYYY-MM-DD
time_format: HH:mm:ss

# Pagination
## Set per_page to 0 to disable pagination
per_page: 10
pagination_dir: page

# Extensions
## Plugins: http://hexo.io/plugins/
## Themes: http://hexo.io/themes/
theme: next

# Deployment
## Docs: http://hexo.io/docs/deployment.html
deploy:
  type: git
  repo: 
    github: **************:tiann/tiann.github.io.git,master

# duoshuo 
# duoshuo_shortname: duoshuo-zygote

# baidu analytics
baidu_analytics: 5ba59f635a6de43d516b52632af83325
# so slow, close it.
#google_analytics: UA-67996870-1

creative_commons: by-nc-sa

since: 2015

# rss
feed:
  type: atom
  path: atom.xml
  limit: 20

# hexo sitemap
sitemap:
  path: sitemap.xml
baidusitemap:
  path: baidusitemap.xml
